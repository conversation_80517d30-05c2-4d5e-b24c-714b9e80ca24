import pytest
import requests
import subprocess
import sys
import time
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 代理服务器配置
HTTP_PROXY = "http://127.0.0.1:44445"
HTTPS_PROXY = "http://127.0.0.1:44445"  # 使用HTTP代理端口处理HTTPS请求
SOCKS5_PROXY = "socks5://127.0.0.1:44447"

# 测试URL
HTTP_TEST_URL = "http://httpbin.org/get"
HTTPS_TEST_URL = "https://www.baidu.com/"
SOCKS5_TEST_URL = "http://httpbin.org/get"

@pytest.fixture(scope="session", autouse=True)
def setup_environment():
    # 启动代理服务器
    process = subprocess.Popen([sys.executable, "httpproxy.py"])
    time.sleep(2)  # 等待服务器启动
    
    yield
    
    # 清理资源
    process.terminate()
    process.wait()

def test_http_proxy():
    # 测试HTTP代理
    proxies = {
        "http": HTTP_PROXY,
        "https": HTTPS_PROXY
    }
    response = requests.get(HTTP_TEST_URL, proxies=proxies)
    assert response.status_code == 200
    data = response.json()
    print("HTTP代理响应:", data)
    assert "url" in data

def test_https_proxy():
    # 测试HTTPS代理
    proxies = {
        "http": HTTP_PROXY,
        "https": HTTPS_PROXY
    }
    response = requests.get(HTTPS_TEST_URL, proxies=proxies, verify=False)
    assert response.status_code == 200
    data = response.json()
    print("HTTPS代理响应:", data)
    assert "url" in data

def test_socks5_proxy():
    # 测试SOCKS5代理
    import socket
    import socks
    
    # 配置SOCKS5代理
    socks.set_default_proxy(socks.SOCKS5, "127.0.0.1", 44447)
    socket.socket = socks.socksocket
    
    response = requests.get(SOCKS5_TEST_URL)
    assert response.status_code == 200
    data = response.json()
    print("SOCKS5代理响应:", data)
    assert "url" in data

def test_multiple_requests():
    # 测试多个并发请求
    proxies = {
        "http": HTTP_PROXY,
        "https": HTTPS_PROXY
    }
    
    urls = [HTTP_TEST_URL] * 3  # 发送3个并发请求
    
    def make_request(url):
        response = requests.get(url, proxies=proxies)
        assert response.status_code == 200
        return response.json()
    
    # 使用线程池并发发送请求
    from concurrent.futures import ThreadPoolExecutor
    with ThreadPoolExecutor(max_workers=3) as executor:
        responses = list(executor.map(make_request, urls))
    
    assert len(responses) == 3
    for response in responses:
        assert "url" in response
        print("并发请求响应:", response)

if __name__ == "__main__":
    pytest.main([__file__, "-v"])