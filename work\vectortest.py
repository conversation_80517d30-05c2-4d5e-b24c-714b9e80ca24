import requests
import json
    # 接口地址
url = "http://10.186.2.176:10010/CUCCAI-intelligent-agent/vectorSearchApi/"

# 请求头（如果有需要）
headers = {
        "Content-Type": "application/json",
}
# 请求体（JSON 格式）
payload = {"appId":"urdDUFiZhKrZi","topK":5,"categoryId":153,"query":"snmp","similarity":0.4}

# 配置socks5代理
proxies = {
    "http": "socks5://192.168.0.105:44444",
    "https": "socks5://192.168.0.105:44444"
}

# 发送 POST 请求
response = requests.post(url, headers=headers, json=payload, proxies=proxies)

# 检查状态码
if response.status_code == 200:
    # 获取响应内容
    data = response.json()
    formatted_json = json.dumps(data, indent=4,ensure_ascii=False)
    print("接口返回的数据：", formatted_json)
else:
    print(f"请求失败，状态码：{response.status_code}")