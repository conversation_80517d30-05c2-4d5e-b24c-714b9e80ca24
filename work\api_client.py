import requests
import json
import time
import logging
import platform
import socket
import traceback
from typing import Dict, Any, Optional, Union, List, Tuple
from requests.exceptions import RequestException, Timeout, ConnectionError
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('api_client')

class APIClient:
    """
    通用API调用客户端，具有高可用性和容错能力
    
    特性：
    1. 自动重试机制（带指数退避）
    2. 超时控制
    3. 异常处理
    4. 详细日志记录
    5. 请求性能监控
    6. 会话复用
    7. 环境信息收集（用于调试）
    """
    def __init__(self, base_url: str, default_headers: Optional[Dict[str, str]] = None, 
                 timeout: int = 10, max_retries: int = 3, retry_delay: int = 1,
                 backoff_factor: float = 0.5, status_forcelist: List[int] = [500, 502, 503, 504],
                 rate_limit_per_second: Optional[int] = None, proxy: Optional[str] = None):
        """
        初始化API客户端
        
        Args:
            base_url: API基础URL
            default_headers: 默认请求头
            timeout: 请求超时时间（秒）
            max_retries: 最大重试次数
            retry_delay: 重试间隔（秒）
            backoff_factor: 重试间隔因子（用于指数退避）
            status_forcelist: 需要重试的HTTP状态码列表
            rate_limit_per_second: 每秒最大请求数（None表示不限制）
            proxy: 代理服务器地址，格式如：socks5://host:port
        """
        self.base_url = base_url.rstrip('/')
        self.default_headers = default_headers or {}
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.rate_limit_per_second = rate_limit_per_second
        self.last_request_time = 0
        self.proxy = proxy
        
        # 创建带有重试机制的会话
        self.session = requests.Session()
        
        # 设置代理（如果提供）
        if self.proxy:
            self.session.proxies = {
                'http': self.proxy,
                'https': self.proxy
            }
            logger.info(f"已设置代理: {self.proxy}")
            
        retry_strategy = Retry(
            total=max_retries,
            backoff_factor=backoff_factor,
            status_forcelist=status_forcelist,
            allowed_methods=["GET", "POST", "PUT", "DELETE", "PATCH"],
            raise_on_redirect=True,
            raise_on_status=True
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # 收集环境信息（用于调试）
        self.environment_info = self._collect_environment_info()
        
    def _collect_environment_info(self) -> Dict[str, str]:
        """
        收集环境信息，用于调试
        
        Returns:
            Dict[str, str]: 环境信息
        """
        try:
            return {
                "platform": platform.platform(),
                "python_version": platform.python_version(),
                "hostname": socket.gethostname(),
                "ip_address": socket.gethostbyname(socket.gethostname())
            }
        except Exception as e:
            logger.warning(f"收集环境信息失败: {e}")
            return {"error": str(e)}
    
    def _rate_limit(self):
        """
        实现请求速率限制
        """
        if self.rate_limit_per_second is not None:
            current_time = time.time()
            elapsed = current_time - self.last_request_time
            min_interval = 1.0 / self.rate_limit_per_second
            
            if elapsed < min_interval:
                sleep_time = min_interval - elapsed
                logger.debug(f"速率限制: 休眠 {sleep_time:.4f} 秒")
                time.sleep(sleep_time)
            
            self.last_request_time = time.time()
    
    def _make_request(self, method: str, endpoint: str, headers: Optional[Dict[str, str]] = None, 
                      params: Optional[Dict[str, Any]] = None, data: Optional[Dict[str, Any]] = None, 
                      json_data: Optional[Dict[str, Any]] = None) -> Tuple[requests.Response, float]:
        """
        发送HTTP请求并处理重试逻辑
        
        Args:
            method: HTTP方法（GET, POST等）
            endpoint: API端点
            headers: 请求头
            params: URL参数
            data: 表单数据
            json_data: JSON数据
            
        Returns:
            Tuple[requests.Response, float]: 响应对象和请求耗时
        
        Raises:
            RequestException: 请求异常
        """
        # 应用速率限制
        self._rate_limit()
        
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        merged_headers = {**self.default_headers, **(headers or {})}
        
        start_time = time.time()
        logger.info(f"发送{method}请求到{url}")
        
        if json_data:
            logger.debug(f"请求体: {json.dumps(json_data, ensure_ascii=False)}")
        
        try:
            response = self.session.request(
                method=method,
                url=url,
                headers=merged_headers,
                params=params,
                data=data,
                json=json_data,
                timeout=self.timeout
            )
            
            # 记录响应时间
            elapsed_time = time.time() - start_time
            logger.info(f"请求完成: {url}, 状态码: {response.status_code}, 耗时: {elapsed_time:.4f}秒")
            
            # 检查响应状态码
            response.raise_for_status()
            return response, elapsed_time
                
        except Timeout as e:
            elapsed_time = time.time() - start_time
            logger.warning(f"请求超时: {url}, 耗时: {elapsed_time:.4f}秒, 错误: {e}")
            raise
                
        except ConnectionError as e:
            elapsed_time = time.time() - start_time
            logger.warning(f"连接错误: {url}, 耗时: {elapsed_time:.4f}秒, 错误: {e}")
            raise
                
        except RequestException as e:
            elapsed_time = time.time() - start_time
            logger.error(f"请求异常: {url}, 耗时: {elapsed_time:.4f}秒, 错误: {e}")
            logger.debug(f"异常详情: {traceback.format_exc()}")
            raise
    
    def get(self, endpoint: str, headers: Optional[Dict[str, str]] = None, 
            params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        发送GET请求
        
        Args:
            endpoint: API端点
            headers: 请求头
            params: URL参数
            
        Returns:
            Dict[str, Any]: 响应数据
        """
        try:
            response, elapsed_time = self._make_request('GET', endpoint, headers=headers, params=params)
            
            # 尝试解析JSON响应
            try:
                result = response.json()
                logger.debug(f"响应内容: {json.dumps(result, ensure_ascii=False)}")
                return result
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析错误: {e}, 响应内容: {response.text}")
                return {
                    "success": False,
                    "error": f"无法解析响应: {str(e)}",
                    "raw_response": response.text
                }
                
        except Exception as e:
            logger.exception(f"GET请求异常: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def post(self, endpoint: str, headers: Optional[Dict[str, str]] = None, 
             params: Optional[Dict[str, Any]] = None, data: Optional[Dict[str, Any]] = None, 
             json_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        发送POST请求
        
        Args:
            endpoint: API端点
            headers: 请求头
            params: URL参数
            data: 表单数据
            json_data: JSON数据
            
        Returns:
            Dict[str, Any]: 响应数据
        """
        try:
            response, elapsed_time = self._make_request('POST', endpoint, headers=headers, params=params, 
                                                      data=data, json_data=json_data)
            
            # 尝试解析JSON响应
            try:
                result = response.json()
                logger.debug(f"响应内容: {json.dumps(result, ensure_ascii=False)}")
                return result
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析错误: {e}, 响应内容: {response.text}")
                return {
                    "success": False,
                    "error": f"无法解析响应: {str(e)}",
                    "raw_response": response.text
                }
                
        except Exception as e:
            logger.exception(f"POST请求异常: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def close(self):
        """
        关闭会话
        """
        self.session.close()
        logger.info("关闭API客户端会话")
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()

class VectorSearchClient:
    """
    向量搜索API客户端
    
    专门用于调用向量搜索API的客户端，具有高可用性和容错能力。
    """
    def __init__(self, base_url: str, app_id: str, default_similarity: float = 0.5, 
                 timeout: int = 10, max_retries: int = 3, backoff_factor: float = 0.5,
                 rate_limit_per_second: Optional[int] = None, proxy: Optional[str] = None):
        """
        初始化向量搜索客户端
        
        Args:
            base_url: API基础URL
            app_id: 应用ID
            default_similarity: 默认相似度阈值
            timeout: 请求超时时间（秒）
            max_retries: 最大重试次数
            backoff_factor: 重试间隔因子（用于指数退避）
            rate_limit_per_second: 每秒最大请求数（None表示不限制）
            proxy: 代理服务器地址，格式如：socks5://host:port
        """
        self.app_id = app_id
        self.default_similarity = default_similarity
        
        # 初始化通用API客户端
        self.client = APIClient(
            base_url=base_url,
            default_headers={"Content-Type": "application/json"},
            timeout=timeout,
            max_retries=max_retries,
            backoff_factor=backoff_factor,
            rate_limit_per_second=rate_limit_per_second,
            proxy=proxy
        )
    
    def search(self, query: str, category_id: str, top_k: int = 2, 
               similarity: Optional[float] = None) -> Dict[str, Any]:
        """
        执行向量搜索
        
        Args:
            query: 查询文本
            category_id: 知识库ID
            top_k: 返回结果数量
            similarity: 相似度阈值
            
        Returns:
            Dict[str, Any]: 搜索结果
        """
        try:
            # 构建请求体
            payload = {
                "appId": self.app_id,
                "topK": top_k,
                "categoryId": category_id,
                "query": query,
                "similarity": similarity or self.default_similarity
            }
            
            logger.info(f"执行向量搜索，查询：{query}，知识库ID：{category_id}")
            start_time = time.time()
            result = self.client.post("", json_data=payload)
            elapsed_time = time.time() - start_time
            
            # 记录性能指标
            logger.info(f"向量搜索完成，耗时：{elapsed_time:.4f}秒")
            
            # 检查结果
            if "success" in result and result["success"] is False:
                logger.warning(f"向量搜索返回错误: {result.get('error')}")
            
            return result
            
        except Exception as e:
            logger.exception(f"向量搜索失败: {e}")
            # 返回一个错误响应而不是抛出异常，提高可用性
            return {
                "success": False,
                "error": str(e),
                "data": None
            }
    
    def health_check(self) -> Dict[str, Any]:
        """
        执行API健康检查
        
        Returns:
            Dict[str, Any]: 健康检查结果
        """
        try:
            # 使用一个简单的查询进行健康检查
            result = self.search(
                query="健康检查",
                category_id="health_check",
                top_k=1
            )
            
            # 检查是否能够连接到API
            if "error" in result and ("连接错误" in str(result["error"]) or "请求超时" in str(result["error"])):
                return {
                    "status": "unavailable",
                    "message": result["error"]
                }
            
            return {
                "status": "available",
                "message": "API可用"
            }
            
        except Exception as e:
            logger.exception(f"健康检查异常: {e}")
            return {
                "status": "error",
                "message": f"健康检查异常: {str(e)}"
            }
    
    def close(self):
        """
        关闭客户端会话
        """
        self.client.close()
        logger.info("关闭向量搜索客户端会话")
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()

# 演示使用方法
def main():
    # 创建向量搜索客户端
    vector_client = VectorSearchClient(
        base_url="http://localhost:8000/CUCCAI-intelligent-agent/vectorSearchApi/",
        app_id="urdDUFiZhKrZi",
        timeout=15,  # 增加超时时间
        max_retries=3,
        backoff_factor=0.5,
        rate_limit_per_second=5,  # 限制每秒请求数
        # proxy="socks5://*************:44444"  # 使用socks5代理
    )
    
    # 定义知识库ID（这里需要替换为实际的知识库ID）
    knowledge_base_id = "165"  # 替换为实际的AI平台知识库ID
    
    # 首先进行健康检查
    health_status = vector_client.health_check()
    print(f"\nAPI健康状态: {health_status['status']}")
    print(f"消息: {health_status['message']}")
    
    if health_status['status'] != "available":
        print("API不可用，退出程序")
        return
    
    try:
        # 执行搜索
        result = vector_client.search(
            query="LTE是什么",
            category_id=knowledge_base_id,
            top_k=2
        )
        
        # 打印结果
        print("\n搜索结果:")
        print(json.dumps(result, ensure_ascii=False, indent=2))
        
        # 分析结果
        if "data" in result and result["data"]:
            print("\n找到的相关内容:")
            for i, item in enumerate(result["data"]):
                print(f"\n结果 {i+1}:")
                if "content" in item:
                    print(f"内容: {item['content']}")
                if "score" in item:
                    print(f"相似度分数: {item['score']}")
        else:
            print("\n未找到相关内容")
        
    except Exception as e:
        print(f"\n执行搜索时发生错误: {e}")
    
    # 确保关闭客户端会话
    finally:
        vector_client.close()

if __name__ == "__main__":
    main()