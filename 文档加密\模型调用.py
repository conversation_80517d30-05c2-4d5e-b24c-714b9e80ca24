from openai import OpenAI
import pandas as pd
import time
def qwen3_30B_A3B(prompt):
    client = OpenAI(
        api_key = 'Empty',
        base_url='http://172.24.122.16:9817/v1'
    )

    chat_response = client.chat.completions.create(
        model='Qwen3-30B-A3B',
        messages=[
           {"role": "user", "content": prompt + '/no_think'}
        ],
        stream=False,
        # max_tokens=16,
        temperature=0
    )

    answer = chat_response.choices[0].message.content
    return answer


def qwen3_32B(prompt):
    client = OpenAI(
        api_key='Empty',
        base_url='http://172.24.122.16:9818/v1'
    )

    chat_response = client.chat.completions.create(
        model='Qwen3-32B',
        messages=[
            {"role": "user", "content": prompt + '/no_think'}
        ],
        stream=False,
        # max_tokens=16,
        temperature=0
    )

    answer = chat_response.choices[0].message.content
    return answer
def read_test_cases_from_excel(file_path: str) -> list:
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path)

        # 初始化测试用例列表
        test_cases = []
        for index, row in df.iterrows():

            question = row['问题']
            standard_answer = row['意图id'] # 确保answer_tool是字符串并转换为小写零
            standard_answer = str(standard_answer).zfill(5)
            model_30b_answer = None
            model_30b_a3b_answer = None

            # 构建测试用例字典
            test_case = {
                'query': question,
                'standard_answer': standard_answer
            }


            # 添加到测试用例列表
            test_cases.append(test_case)

        return test_cases

    except Exception as e:
        print(f"读取Excel文件时出错: {str(e)}")
        raise
def save_test_results_to_excel(file_path: str, test_cases: list):
    """
    将测试结果保存到Excel文件中

    Args:
        file_path (str): Excel文件路径
        test_cases (list): 包含测试用例结果的字典列表
    """
    try:
        # 将测试用例列表转换为DataFrame
        df = pd.DataFrame(test_cases)

        # 保存到Excel文件
        df.to_excel(file_path, index=False)

    except Exception as e:
        pass
        raise
if __name__  == "__main__":
    prompt = ''
    file_path = './全量提示词.txt'
    prompt = ''
    with open(file_path, 'r',encoding='utf-8') as file:
        lines = file.readlines()
        for line in lines:
            # 在这里对每一行的内容进行处理
            prompt += line# 例如，打印每行内容去除首尾空格和换行符

    print(prompt)
    print(qwen3_32B(prompt))