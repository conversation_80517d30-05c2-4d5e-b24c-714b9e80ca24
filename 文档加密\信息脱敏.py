import re
import random


# IP正则匹配
def find_sensitive_fields(text: str) -> list:
    ip_pattern = r'\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b'
    mac_pattern = r'([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})'
    return re.findall(ip_pattern, text) + re.findall(mac_pattern, text)


def iprandom(ipstr):
    ip = [int(num) for num in ipstr.split('.')]
    result = []

    if ip[0] == 255:
        result.append(ip[0])
    elif ip[0] == 0:
        random.seed("dfwe3rffe")
        random_number = random.randint(50, 100)
        result.append(int(ip[0] + random_number))
    else:
        random.seed("aewxsd5d")
        random_number = random.randint(50, 100)
        result.append(int(ip[0] - random_number * random.random()))

    if ip[1] == 255:
        result.append(ip[1])
    elif ip[1] == 0:
        random.seed("sfeg8wrg")
        random_number = random.randint(50, 100)
        result.append(int(ip[1] + random_number))
    else:
        random.seed("wriy785")
        random_number = random.randint(50, 100)
        result.append(int(ip[1] + random_number * random.random()))

    if ip[2] == 255:
        result.append(ip[2])
    elif ip[2] == 0:
        random.seed("pqoiuf21")
        random_number = random.randint(50, 100)
        result.append(int(ip[2] + random_number))
    else:
        random.seed("wsj89cm3")
        random_number = random.randint(50, 100)
        result.append(int(ip[2] + random_number * random.random()))

    if ip[3] == 255:
        result.append(ip[3])
    elif ip[3] == 0:
        random.seed("4erteuf21")
        random_number = random.randint(50, 100)
        result.append(int(ip[3] + random_number))
    else:
        random.seed("podiannao1")
        random_number = random.randint(0, 50)
        result.append(int(ip[3] - random_number))

    for i in range(0, 4):
        if result[i] < 0:
            result[i] = abs(result[i])
        if result[i] > 255:
            result[i] = result[i] - 100

    return '.'.join([str(i) for i in result])


resultlines = []
with open('../fw去重.txt', 'r',encoding="utf-8") as f:
    lines = f.readlines()
    for line in lines:
        iplist = find_sensitive_fields(line)
        if len(iplist) > 0:
            for ip in iplist:
                line = line.replace(ip, iprandom(ip))
        resultlines.append(line)
with open('../fw脱敏.txt', 'w',encoding="utf-8") as f:
    f.writelines(resultlines)
