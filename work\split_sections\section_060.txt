#
//这是SNMP MIB视图和用户配置，创建四个MIB视图nt、rd、wt和iso-view，均包含整个ISO MIB树，创建SNMPv3用户admin和uhmroot，将uhmroot用户加入dc-admin组，配置uhmroot用户使用SHA认证模式和AES128加密模式，并设置加密的认证密码和隐私密码。这些配置定义了SNMP用户的访问权限和安全参数，确保只有授权用户能够安全地访问和管理设备。//
snmp-agent mib-view included nt iso
snmp-agent mib-view included rd iso
snmp-agent mib-view included wt iso
snmp-agent mib-view included iso-view iso
snmp-agent usm-user v3 admin
snmp-agent usm-user v3 uhmroot
snmp-agent usm-user v3 uhmroot group dc-admin
snmp-agent usm-user v3 uhmroot authentication-mode sha cipher %^%#[t9u%9i*fIb6,bSnc:.)4CND9&^ct9>:#ZYuN9C9%^%#
snmp-agent usm-user v3 uhmroot privacy-mode aes128 cipher %^%#|aY(B5g)}L#AyBWW)KnBVM5F.8r%o~Q{TWW/sEEC%^%#
