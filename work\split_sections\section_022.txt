#
//这是另一个IP VPN实例配置，创建名为VPC-wlzys-APP_0000013的VPN实例，同时配置IPv4和IPv6地址族，两者都设置路由区分符为78:50019，配置VPN目标为0:50019用于导入和导出扩展团体属性，包括EVPN路由。该VPN实例描述为微沃云平台的应用VPC，并绑定VXLAN网络标识符(VNI)为50019，支持双栈(IPv4/IPv6)网络环境下的租户隔离和网络虚拟化。//
ip vpn-instance VPC-wlzys-APP_0000013
 ipv4-family
  route-distinguisher 78:50019
  vpn-target 0:50019 export-extcommunity
  vpn-target 0:50019 export-extcommunity evpn
  vpn-target 0:50019 import-extcommunity
  vpn-target 0:50019 import-extcommunity evpn
 description weiwo(9f52ee18f3bb41e3825280792429a1b1)-fusionsphere_VPC-wlzys-APP
 ipv6-family 
  route-distinguisher 78:50019
  vpn-target 0:50019 export-extcommunity
  vpn-target 0:50019 export-extcommunity evpn
  vpn-target 0:50019 import-extcommunity
  vpn-target 0:50019 import-extcommunity evpn
 vxlan vni 50019
