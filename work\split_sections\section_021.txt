#
//这是IP VPN实例配置，创建名为ManageOne_0000011的VPN实例，配置IPv4地址族，设置路由区分符为78:50017，配置VPN目标为0:50017用于导入和导出扩展团体属性，包括EVPN路由。该VPN实例描述为ManageOne云平台相关，并绑定VXLAN网络标识符(VNI)为50017，用于实现租户隔离和网络虚拟化。//
ip vpn-instance ManageOne_0000011
 ipv4-family
  route-distinguisher 78:50017
  vpn-target 0:50017 export-extcommunity
  vpn-target 0:50017 export-extcommunity evpn
  vpn-target 0:50017 import-extcommunity
  vpn-target 0:50017 import-extcommunity evpn
 description ManageOne(0c7f69422055424a8734d71ce7a44979)-fusionsphere_ManageOne
 vxlan vni 50017
