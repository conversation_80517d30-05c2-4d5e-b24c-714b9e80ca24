#
//这是时区配置，设置设备时区为北京时间，相对于UTC/GMT时间增加8小时。//
clock timezone BJ add 08:00:00
#
//这是系统名称配置，将设备的主机名设置为"SW"，用于在网络中标识该设备。//
sysname SW
#
//这是分布式框架系统(DFS)组配置，创建编号为1的DFS组，设置优先级为150，并指定源IP地址为************，用于多设备协同工作时的通信。//
dfs-group 1
 priority 150
 source ip ************ 
#
//这是日志中心配置，设置默认日志源通过通道2发送警告级别及以上的日志，指定日志主机源接口为LoopBack0，并将日志发送到IP地址为************的日志服务器。//
info-center source default channel 2 log level warning   
info-center loghost source LoopBack0
info-center loghost ************
#
//这是系统资源配置，启用大型路由表资源模式，为路由表分配更多的系统资源，适用于需要存储大量路由条目的场景。//
system resource large-route
#
//这是设备板卡配置，指定槽位1的板卡类型为CE6855-48S6Q-HI，这是一种高性能交换机板卡，具有48个SFP+端口和6个QSFP+端口。//
device board 1 board-type CE6855-48S6Q-HI
#
//这是默认丢包策略配置，使用系统默认的丢包配置文件，用于在网络拥塞时控制数据包的丢弃行为。//
drop-profile default
#
//这是数据中心桥接(DCB)优先级流量控制(PFC)配置，启用PFC功能，用于防止网络拥塞导致的数据包丢失，特别适用于无损网络环境。//
dcb pfc
#
//这是数据中心桥接(DCB)增强型传输选择(ETS)配置，使用默认的ETS配置文件，用于为不同类型的网络流量分配带宽资源。//
dcb ets-profile default
#
//这是网络时间协议(NTP)配置，禁用IPv6 NTP服务器，设置NTP源接口为LoopBack0，并配置两个NTP单播服务器，其中************为首选服务器，************为备用服务器，用于同步设备时间。//
ntp ipv6 server disable
ntp source-interface LoopBack0
ntp unicast-server ************ preferred
ntp unicast-server ************
#
//这是转发资源分配配置，为三层转发分配大型覆盖网络资源，优化VXLAN等覆盖网络技术的性能，适用于大规模数据中心网络环境。//
assign forward layer-3 resource large-overlay
#
//这是ARP资源节省模式配置，启用ARP资源节省模式，减少ARP表项占用的系统资源，适用于ARP表项较多的网络环境。//
arp resource-saving-mode
#
//这是IPv6转发资源分配配置，为IPv6长掩码路由分配1024个资源项，优化IPv6路由查找性能，支持更多的IPv6长掩码路由条目。//
assign forward ipv6 longer-mask resource 1024
#
//这是VLAN保留配置，将VLAN ID 4060到4063保留给主接口使用，防止这些VLAN ID被其他功能占用，确保主接口功能正常运行。//
vlan reserved for main-interface 4060 to 4063
#
//这是MAC地址震荡周期性告警配置，启用MAC地址震荡的周期性告警功能，当网络中出现MAC地址频繁变动时，设备会定期发送告警信息，帮助网络管理员及时发现和解决网络环路等问题。//
mac-address flapping periodical trap enable
#
//这是生成树协议(STP)配置，设置STP模式为快速生成树协议(RSTP)，并启用虚拟STP功能，用于防止网络环路，提高网络的可靠性和稳定性，同时支持虚拟网络环境下的STP功能。//
stp mode rstp
stp v-stp enable
#
//这是以太网VPN覆盖网络配置，启用EVPN覆盖网络功能，用于构建基于VXLAN的大二层网络，提供多租户隔离、虚拟机迁移等数据中心网络服务。//
evpn-overlay enable
#
//这是链路聚合控制协议(LACP)多机链路聚合组(M-LAG)配置，设置M-LAG系统ID为00e0-fc00-0001，用于多台设备之间的链路聚合，提高网络带宽和可靠性。//
lacp m-lag system-id 00e0-fc00-0001
#
//这是Telnet服务配置，禁用IPv4和IPv6的Telnet服务器功能，提高设备的安全性，防止通过不安全的Telnet协议远程访问设备。//
telnet server disable
telnet ipv6 server disable
#
//这是差分服务(DiffServ)域配置，使用默认的DiffServ域，用于实现网络服务质量(QoS)管理，对不同类型的网络流量进行分类和优先级处理。//
diffserv domain default
#
//这是IP VPN实例配置，创建名为ManageOne_0000011的VPN实例，配置IPv4地址族，设置路由区分符为78:50017，配置VPN目标为0:50017用于导入和导出扩展团体属性，包括EVPN路由。该VPN实例描述为ManageOne云平台相关，并绑定VXLAN网络标识符(VNI)为50017，用于实现租户隔离和网络虚拟化。//
ip vpn-instance ManageOne_0000011
 ipv4-family
  route-distinguisher 78:50017
  vpn-target 0:50017 export-extcommunity
  vpn-target 0:50017 export-extcommunity evpn
  vpn-target 0:50017 import-extcommunity
  vpn-target 0:50017 import-extcommunity evpn
 description ManageOne(0c7f69422055424a8734d71ce7a44979)-fusionsphere_ManageOne
 vxlan vni 50017
#
//这是另一个IP VPN实例配置，创建名为VPC-wlzys-APP_0000013的VPN实例，同时配置IPv4和IPv6地址族，两者都设置路由区分符为78:50019，配置VPN目标为0:50019用于导入和导出扩展团体属性，包括EVPN路由。该VPN实例描述为微沃云平台的应用VPC，并绑定VXLAN网络标识符(VNI)为50019，支持双栈(IPv4/IPv6)网络环境下的租户隔离和网络虚拟化。//
ip vpn-instance VPC-wlzys-APP_0000013
 ipv4-family
  route-distinguisher 78:50019
  vpn-target 0:50019 export-extcommunity
  vpn-target 0:50019 export-extcommunity evpn
  vpn-target 0:50019 import-extcommunity
  vpn-target 0:50019 import-extcommunity evpn
 description weiwo(9f52ee18f3bb41e3825280792429a1b1)-fusionsphere_VPC-wlzys-APP
 ipv6-family 
  route-distinguisher 78:50019
  vpn-target 0:50019 export-extcommunity
  vpn-target 0:50019 export-extcommunity evpn
  vpn-target 0:50019 import-extcommunity
  vpn-target 0:50019 import-extcommunity evpn
 vxlan vni 50019
#
//这是软件定义网络(SDN)代理配置，设置SDN控制器IP地址为*************和*************，并为每个控制器配置OpenFlow代理，指定传输地址为************。这使设备能够接受SDN控制器的集中管理，支持网络的可编程性和灵活控制。//
sdn agent
 controller-ip *************
  openflow agent
   transport-address ************
 controller-ip *************
  openflow agent
   transport-address ************
#
//这是桥域配置，创建桥域31，将其与VXLAN网络标识符(VNI)5037绑定，并配置EVPN相关参数，包括路由区分符78:5037和VPN目标0:5037用于导入和导出扩展团体属性。桥域用于实现二层网络虚拟化，将VXLAN隧道与本地二层网络连接起来，支持跨数据中心的二层网络扩展。//
bridge-domain 31
 vxlan vni 5037
 evpn
  route-distinguisher 78:5037
  vpn-target 0:5037 export-extcommunity
  vpn-target 0:5037 import-extcommunity
#
//这是访问控制列表(ACL)配置，创建编号为2222的ACL，描述为SNMP，规则5允许源IP地址为************且掩码为**********的流量通过。该ACL用于控制SNMP访问权限，只允许特定网段的设备访问设备的SNMP服务，提高网络安全性。//
acl number 2222
 description SNMP
 rule 5 permit source ************ **********
#
//这是VTY访问控制列表配置，创建编号为3333的ACL，描述为vty，包含多条规则允许特定IP地址或网段访问设备的VTY（虚拟终端）接口。规则5-30允许公网IP地址访问，规则35-65允许管理VPN实例中的特定IP地址访问，规则70允许另一个公网IP地址段访问。这些规则共同构成了设备远程管理访问控制策略，确保只有授权的管理地址能够远程登录设备，提高设备的安全性。//
acl number 3333
 description vty
 rule 5 permit ip source ************ *************
 rule 10 permit ip source ************** ************
 rule 15 permit ip source ************* ************
 rule 20 permit ip source ************* ************
 rule 25 permit ip source ************* ************
 rule 30 permit ip source ************ 0
 rule 35 permit ip vpn-instance mgmt source ************ *************
 rule 40 permit ip vpn-instance mgmt source ************** ************
 rule 45 permit ip vpn-instance mgmt source ************* ************
 rule 50 permit ip vpn-instance mgmt source ************* ************
 rule 55 permit ip vpn-instance mgmt source ************* ************
 rule 60 permit ip vpn-instance mgmt source ************ 0
 rule 65 permit ip vpn-instance mgmt source ************* **********
 rule 70 permit ip source ************* **********
#
//这是AAA（认证、授权、计费）配置部分，包括默认认证方案、默认授权方案、默认计费方案，以及默认域和默认管理域的配置。AAA框架用于控制用户对网络资源的访问，提供身份验证、授权控制和操作记录功能，增强网络安全性和可审计性。//
 authentication-scheme default
 #
 authorization-scheme default
 #
 accounting-scheme default
 #
 domain default
 #
 domain default_admin
#
//这是堆叠配置，启用设备堆叠功能，允许多台物理设备作为一个逻辑设备进行管理，提高网络的可靠性和可扩展性。//
stack
#
//这是许可证配置，用于管理设备的软件许可证，控制设备功能的启用和使用权限。//
license
#
//这是VXLAN桥接域接口配置，创建Vbdif1139接口，将其绑定到VPC_jsAIzx_DB1_50513 VPN实例，配置IP地址为*************/24，MAC地址为0000-5e00-0102，启用VXLAN任播网关功能和ARP主机信息收集功能。该接口作为VXLAN网络的三层网关，提供跨VXLAN网络的三层转发服务。//
interface Vbdif1139
 ip binding vpn-instance VPC_jsAIzx_DB1_50513
 ip address ************* **************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
//这是另一个VXLAN桥接域接口配置，创建Vbdif1143接口，将其绑定到VPC_wlcpyfs_APP_50261 VPN实例，同时配置IPv4地址**************/24和IPv6地址2408:81B0:A00:1:1500:10:0:1/96，设置IPv6邻居发现路由器通告前缀，配置MAC地址为0000-5e00-0102，启用IPv6邻居发现相关功能，并启用VXLAN任播网关和主机信息收集功能。该接口支持双栈(IPv4/IPv6)环境下的VXLAN网络三层网关服务。//
interface Vbdif1143
 ip binding vpn-instance VPC_wlcpyfs_APP_50261
 ipv6 enable
 ip address ************** ***************
 ipv6 address 2408:81B0:A00:1:1500:10:0:1/96
 ipv6 nd ra prefix 2408:81B0:A00:1:1500:10::/96 0 0 no-autoconfig off-link
 mac-address 0000-5e00-0102
 ipv6 nd ra halt disable
 ipv6 nd autoconfig managed-address-flag
 ipv6 nd autoconfig other-flag
 ipv6 nd collect host enable
 vxlan anycast-gateway enable
 arp collect host enable
#
//这是第三个VXLAN桥接域接口配置，创建Vbdif1149接口，将其绑定到VPC_wlcpyfs_DB_50262 VPN实例，配置IP地址为**************/24，MAC地址为0000-5e00-0102，启用VXLAN任播网关和ARP主机信息收集功能。该接口作为数据库VPC的VXLAN网络三层网关。//
interface Vbdif1149
 ip binding vpn-instance VPC_wlcpyfs_DB_50262
 ip address ************** ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable

//这是管理以太网接口配置，配置MEth0/0/0接口，将其绑定到mgmt VPN实例，设置IP地址为************/18。该接口用于设备的带外管理，与数据平面隔离，提供独立的管理通道。//
interface MEth0/0/0
 ip binding vpn-instance mgmt
 ip address ************ **************
#
//这是以太网汇聚接口配置，创建Eth-Trunk0接口，描述为连接到ZY1B3F1-05P12L44U-Pub-HW6855-YWLF01设备的eth-trunk0，配置为LACP静态模式，并设置为对等链路（peer-link）。该接口用于设备间的高带宽互联，作为M-LAG（多机链路聚合组）的对等链路，确保双机热备份系统的可靠通信。//
interface Eth-Trunk0
 description "To-[ZY1B3F1-05P12L44U-Pub-HW6855-YWLF01]-eth-trunk0"
 mode lacp-static
 peer-link 1
#
//这是三层以太网汇聚接口配置，创建Eth-Trunk11接口，禁用端口交换功能（转为三层接口），描述为连接到ZY1B3F1-06P15L03U-Pub-HW12816-YWSP01设备的eth-trunk95，配置IP地址为************/27，设置OSPF邻居最大开销保持计时器为800000毫秒，配置为LACP静态模式。该接口用于设备间的三层互联，支持OSPF路由协议，提供网络的三层连通性。//
interface Eth-Trunk11
 undo portswitch
 description To-[ZY1B3F1-06P15L03U-Pub-HW12816-YWSP01]-eth-trunk95
 ip address ************ ***************
 ospf peer hold-max-cost timer 800000
 mode lacp-static
#
//这是二层以太网汇聚接口配置，创建Eth-Trunk61接口，启用STP边缘端口功能，配置为LACP动态模式，将其加入DFS组1的M-LAG 61，设置为NVO3（网络虚拟化覆盖）接入模式。该接口用于连接下游设备，支持多机链路聚合，提供冗余连接和负载均衡，同时作为VXLAN网络的接入端口。//
interface Eth-Trunk61
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 61
 port nvo3 mode access
#
//这是二层以太网汇聚子接口配置，创建Eth-Trunk61.1子接口，工作在二层模式，描述为XNH_DETECT，配置802.1Q封装，VLAN ID为901，并将其加入桥域31。该子接口用于连接特定VLAN的流量，并通过桥域与VXLAN网络关联，实现VLAN到VXLAN的映射。//
interface Eth-Trunk61.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
//这是另一个二层以太网汇聚接口配置，创建Eth-Trunk95接口，启用STP边缘端口功能，配置为LACP动态模式，将其加入DFS组1的M-LAG 95，设置为NVO3接入模式。该接口与Eth-Trunk61类似，用于连接下游设备，支持多机链路聚合和VXLAN网络接入。//
interface Eth-Trunk95
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 95
 port nvo3 mode access
#
//这是Eth-Trunk95的第一个子接口配置，创建Eth-Trunk95.1子接口，工作在二层模式，描述为XNH_DETECT，配置802.1Q封装，VLAN ID为901，并将其加入桥域31。该子接口与Eth-Trunk61.1配置相同，用于冗余连接，确保VLAN 901流量可以通过两个不同的汇聚接口接入VXLAN网络。//
interface Eth-Trunk95.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
//这是Eth-Trunk95的第二个子接口配置，创建Eth-Trunk95.2子接口，工作在二层模式，配置802.1Q封装，VLAN ID为100，并将其加入桥域424。该子接口用于将VLAN 100的流量映射到桥域424，实现特定VLAN到VXLAN的映射。//
interface Eth-Trunk95.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 424
#
//这是Eth-Trunk95的第三个子接口配置，创建Eth-Trunk95.3子接口，工作在二层模式，配置802.1Q封装，VLAN ID为101，并将其加入桥域1149。该子接口用于将VLAN 101的流量映射到桥域1149，实现特定VLAN到VXLAN的映射。//
interface Eth-Trunk95.3 mode l2
 encapsulation dot1q vid 101
 bridge-domain 1149
#
//这是Eth-Trunk95的第六个子接口配置，创建Eth-Trunk95.6子接口，工作在二层模式，配置802.1Q封装，VLAN ID为108，并将其加入桥域895。该子接口用于将VLAN 108的流量映射到桥域895，实现特定VLAN到VXLAN的映射。//
interface Eth-Trunk95.6 mode l2
 encapsulation dot1q vid 108
 bridge-domain 895
#
//这是Eth-Trunk95的第七个子接口配置，创建Eth-Trunk95.7子接口，工作在二层模式，配置802.1Q封装，VLAN ID为112，并将其加入桥域53。该子接口用于将VLAN 112的流量映射到桥域53，实现特定VLAN到VXLAN的映射。//
interface Eth-Trunk95.7 mode l2
 encapsulation dot1q vid 112
 bridge-domain 53
#
//这是Eth-Trunk95的第十一个子接口配置，创建Eth-Trunk95.11子接口，工作在二层模式，配置802.1Q封装，VLAN ID为109，并将其加入桥域263。该子接口用于将VLAN 109的流量映射到桥域263，实现特定VLAN到VXLAN的映射。//
interface Eth-Trunk95.11 mode l2
 encapsulation dot1q vid 109
 bridge-domain 263
#
//这是Eth-Trunk95的第十二个子接口配置，创建Eth-Trunk95.12子接口，工作在二层模式，配置802.1Q封装，VLAN ID为110，并将其加入桥域262。该子接口用于将VLAN 110的流量映射到桥域262，实现特定VLAN到VXLAN的映射。//
interface Eth-Trunk95.12 mode l2
 encapsulation dot1q vid 110
 bridge-domain 262
#
//这是Eth-Trunk95的第十三个子接口配置，创建Eth-Trunk95.13子接口，工作在二层模式，配置802.1Q封装，VLAN ID为111，并将其加入桥域103。该子接口用于将VLAN 111的流量映射到桥域103，实现特定VLAN到VXLAN的映射。//
interface Eth-Trunk95.13 mode l2
 encapsulation dot1q vid 111
 bridge-domain 103
#
//这是Eth-Trunk95的第十五个子接口配置，创建Eth-Trunk95.15子接口，工作在二层模式，配置802.1Q封装，VLAN ID为113，并将其加入桥域166。该子接口用于将VLAN 113的流量映射到桥域166，实现特定VLAN到VXLAN的映射。//
interface Eth-Trunk95.15 mode l2
 encapsulation dot1q vid 113
 bridge-domain 166
#
//这是Eth-Trunk95的第十六个子接口配置，创建Eth-Trunk95.16子接口，工作在二层模式，配置802.1Q封装，VLAN ID为114，并将其加入桥域72。该子接口用于将VLAN 114的流量映射到桥域72，实现特定VLAN到VXLAN的映射。//
interface Eth-Trunk95.16 mode l2
 encapsulation dot1q vid 114
 bridge-domain 72
#
//这是10GE物理接口配置，配置10GE1/0/1接口，描述为连接到ZY1B3F1-05P12L04U-XNH-LC3-SVR01设备的TBD2接口，将其加入eth-trunk 61链路聚合组，配置风暴抑制功能（限制未知单播流量为5%，组播和广播流量为1000包/秒），启用CRC错误统计触发接口关闭功能，设置CRC错误统计告警阈值为10秒内100个错误，指定接口使用10GBASE-FIBER光模块。该接口用于连接服务器，并通过链路聚合提高带宽和可靠性。//
interface 10GE1/0/1
 description "To-[ZY1B3F1-05P12L04U-XNH-LC3-SVR01]-TBD2"
 eth-trunk 61
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
//这是40GE物理接口配置，配置40GE1/0/6接口，描述为连接到ZY1B3F1-05P12L44U-Pub-HW6855-YWLF01设备的40GE1/0/6接口，将其加入eth-trunk 0链路聚合组，启用CRC错误统计触发接口关闭功能，设置CRC错误统计告警阈值为10秒内100个错误，指定接口使用40GBASE-FIBER光模块。该接口用于设备间的高速互联，作为对等链路的物理成员接口，提供高带宽的设备间通信。//
interface 40GE1/0/6
 description "To-[ZY1B3F1-05P12L44U-Pub-HW6855-YWLF01]-40GE1/0/6"
 eth-trunk 0
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 40GBASE-FIBER
#
//这是环回接口配置，配置LoopBack0接口，设置IP地址为************/32。环回接口是一个虚拟接口，不依赖于物理链路状态，通常用作设备的管理IP地址、路由协议的Router ID、BGP对等体连接地址等，提高网络的稳定性。//
interface LoopBack0
 ip address ************ ***************
#
//这是第二个环回接口配置，配置LoopBack1接口，启用IPv6功能，设置IPv4地址为************/32。该环回接口支持双栈(IPv4/IPv6)，可用于IPv6路由协议的Router ID、BGP对等体连接地址等，为IPv6网络提供稳定的标识。//
interface LoopBack1
 ipv6 enable
 ip address ************ ***************
#
//这是网络虚拟化边缘(NVE)接口配置，创建Nve1接口，设置源地址为***********，配置多个VXLAN网络标识符(VNI)5037、6007和6010，使用BGP协议自动发现和建立VXLAN隧道，设置MAC地址为0000-5e00-0135。NVE接口是VXLAN网络的隧道端点，负责VXLAN报文的封装和解封装，实现跨数据中心的二层网络扩展。//
interface Nve1
 source ***********
 vni 5037 head-end peer-list protocol bgp
 vni 6007 head-end peer-list protocol bgp
 vni 6010 head-end peer-list protocol bgp
 mac-address 0000-5e00-0135
#
//这是NULL接口配置，创建NULL0接口。NULL接口是一个特殊的接口，发送到该接口的所有数据包都会被丢弃，通常用于黑洞路由，防止路由环路或实现流量过滤。//
interface NULL0
#
//这是监控链路组配置，创建监控链路组1，设置40GE1/0/1和40GE1/0/2为上行链路，10GE1/0/47和10GE1/0/48为下行链路（分别关联下行ID 47和48），配置恢复计时器为60秒。监控链路组用于监控上行链路状态，当所有上行链路都故障时，自动关闭相关的下行链路，防止流量黑洞，当上行链路恢复后，等待60秒再恢复下行链路，避免链路状态频繁变化。//
monitor-link group 1
 port 40GE1/0/1 uplink
 port 40GE1/0/2 uplink
 port 10GE1/0/47 downlink 47
 port 10GE1/0/48 downlink 48
 timer recover-time 60
#
//这是边界网关协议(BGP)配置的开始部分，配置BGP进程100，设置路由器ID为************，配置四个BGP对等体************、************、************和************，它们都属于AS 100（与本地AS相同，为IBGP对等体），并使用LoopBack0接口作为连接接口。BGP用于在自治系统之间或内部交换路由信息，在VXLAN EVPN网络中承担控制平面的角色，负责VTEP发现和MAC/IP路由信息的分发。//
bgp 100
 router-id ************
 peer ************ as-number 100
 peer ************ connect-interface LoopBack0
 peer ************ as-number 100
 peer ************ connect-interface LoopBack0
 peer ************ as-number 100
 peer ************ connect-interface LoopBack0
 peer ************ as-number 100
 peer ************ connect-interface LoopBack0
 #
 //这是BGP IPv4单播地址族配置，启用四个BGP对等体************、************、************和************在IPv4单播地址族下的能力，使它们能够交换IPv4单播路由信息。//
 ipv4-family unicast
  peer ************ enable
  peer ************ enable
  peer ************ enable
  peer ************ enable
 #
 //这是BGP VPN实例IPv4地址族配置，配置ManageOne_0000011 VPN实例的IPv4地址族，允许导入默认路由，导入直连和静态路由，设置最大负载均衡路径数为32，并将路由通告到L2VPN EVPN地址族。这使得VPN实例内的IPv4路由可以通过EVPN进行分发，实现跨数据中心的VPN路由同步。//
 ipv4-family vpn-instance ManageOne_0000011
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 //这是另一个BGP VPN实例IPv4地址族配置，配置VPC-wlzys-APP_0000013 VPN实例的IPv4地址族，允许导入默认路由，导入直连和静态路由，设置最大负载均衡路径数为32，并将路由通告到L2VPN EVPN地址族。这使得该VPC应用VPN实例内的IPv4路由可以通过EVPN进行分发。//
 ipv4-family vpn-instance VPC-wlzys-APP_0000013
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 //这是BGP VPN实例IPv6地址族配置，配置VPC-wlzys-APP_0000013 VPN实例的IPv6地址族，允许导入默认路由，导入直连和静态路由，设置最大负载均衡路径数为32，并将路由通告到L2VPN EVPN地址族。这使得该VPC应用VPN实例内的IPv6路由可以通过EVPN进行分发，支持双栈环境下的路由同步。//
 ipv6-family vpn-instance VPC-wlzys-APP_0000013
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 //这是另一个BGP VPN实例IPv6地址族配置，配置VPC_NMS_JT_0000089 VPN实例的IPv6地址族，允许导入默认路由，导入直连和静态路由，设置最大负载均衡路径数为32，并将路由通告到L2VPN EVPN地址族。这使得该网络管理系统VPN实例内的IPv6路由可以通过EVPN进行分发。//
 ipv6-family vpn-instance VPC_NMS_JT_0000089
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 //这是BGP L2VPN EVPN地址族配置，启用VPN目标策略，配置四个BGP对等体************、************、************和************在EVPN地址族下的能力，并允许它们通告集成路由桥接(IRB)和IPv6集成路由桥接(IRBv6)路由。EVPN地址族用于在VXLAN网络中分发MAC地址、IP地址和路由信息，支持二层和三层服务的融合，实现虚拟机迁移、多活数据中心等高级网络功能。//
 l2vpn-family evpn
  policy vpn-target
  peer ************ enable
  peer ************ advertise irb
  peer ************ advertise irbv6
  peer ************ enable
  peer ************ advertise irb
  peer ************ advertise irbv6
  peer ************ enable
  peer ************ advertise irb
  peer ************ advertise irbv6
  peer ************ enable
  peer ************ advertise irb
  peer ************ advertise irbv6
#
//这是开放最短路径优先(OSPF)协议配置，创建OSPF进程10，设置路由器ID为************，配置SPF计算间隔、LSA生成间隔和LSA到达间隔均使用智能定时器模式（初始值、增量值和最大值均为50毫秒），创建OSPF区域***********，并在该区域内通告多个网段，包括环回接口地址和物理接口地址。OSPF用于网络内部的路由信息交换，通过最短路径优先算法计算最佳路由，提供快速的路由收敛。//
ospf 10 router-id ************
 spf-schedule-interval intelligent-timer 50 50 50
 lsa-originate-interval intelligent-timer 50 50 50
 lsa-arrival-interval intelligent-timer 50 50 50
 area ***********
  network ************ ***********
  network ************ ***********
  network *********** ***********
  network ************ ***********
  network ************ ***********
  network ************ ***********
#
//这是静态路由配置，在mgmt VPN实例中创建一条静态路由，目的网段为***********/32，下一跳为通过MEth0/0/0接口到达的*************，描述为mgmt。该静态路由用于管理VPN实例中的特定流量转发，确保管理流量能够正确到达目标网络。//
ip route-static vpn-instance mgmt *********** *********** MEth0/0/0 ************* description mgmt
#
//这是简单网络管理协议(SNMP)代理配置，启用SNMP代理功能，设置本地引擎ID为800007DB039C741A50A2C1，配置加密的只读团体字，并将其与ACL 2222关联。SNMP用于网络设备的监控和管理，通过团体字进行访问控制，结合ACL可以限制只有特定网段的管理站能够访问设备的SNMP信息。//
snmp-agent
snmp-agent local-engineid 800007DB039C741A50A2C1
snmp-agent community read cipher %^%#a}-Q%WL5d6agCD8Y^+WQQ/%#"}deTO^.{1#XOO"T_0kt6i4&}*!TI!=LKQ(7iOw+B("`h+;>{ZH<t;d-%^%# acl 2222
#
//这是SNMP系统信息和安全配置，启用所有版本的SNMP协议，创建SNMPv3安全组dc-admin，配置隐私安全级别，设置读视图rd、写视图wt和通知视图nt，配置两个SNMP告警目标主机************和************，使用加密的安全名称参数。这些配置增强了SNMP的安全性，并确保告警信息能够发送到指定的网管服务器。//
snmp-agent sys-info version all
snmp-agent group v3 dc-admin privacy read-view rd write-view wt notify-view nt
snmp-agent target-host trap address udp-domain ************ params securityname cipher %^%#w/5&T2u"t5DI3>PgpX^E.ZS7Xv06&*V1(.WuY|YM%^%#
snmp-agent target-host trap address udp-domain ************ params securityname cipher %^%#ACw70["Y|C$>TkG=Yp;(q1\3+Xv`Y84qVWU[0~2K%^%#
#
//这是SNMP MIB视图和用户配置，创建四个MIB视图nt、rd、wt和iso-view，均包含整个ISO MIB树，创建SNMPv3用户admin和uhmroot，将uhmroot用户加入dc-admin组，配置uhmroot用户使用SHA认证模式和AES128加密模式，并设置加密的认证密码和隐私密码。这些配置定义了SNMP用户的访问权限和安全参数，确保只有授权用户能够安全地访问和管理设备。//
snmp-agent mib-view included nt iso
snmp-agent mib-view included rd iso
snmp-agent mib-view included wt iso
snmp-agent mib-view included iso-view iso
snmp-agent usm-user v3 admin
snmp-agent usm-user v3 uhmroot
snmp-agent usm-user v3 uhmroot group dc-admin
snmp-agent usm-user v3 uhmroot authentication-mode sha cipher %^%#[t9u%9i*fIb6,bSnc:.)4CND9&^ct9>:#ZYuN9C9%^%#
snmp-agent usm-user v3 uhmroot privacy-mode aes128 cipher %^%#|aY(B5g)}L#AyBWW)KnBVM5F.8r%o~Q{TWW/sEEC%^%#
#
//这是SNMP告警源配置，设置SNMP告警的源接口为LoopBack0。这确保SNMP告警报文的源IP地址为LoopBack0接口的IP地址，提高告警的可靠性和一致性。//
snmp-agent trap source LoopBack0
#
//这是SNMP告警使能配置，全局启用SNMP告警功能。这允许设备在发生重要事件（如接口状态变化、温度超限等）时，主动向网管服务器发送告警信息，便于网络管理员及时发现和处理网络问题。//
snmp-agent trap enable
#
//这是链路层发现协议(LLDP)配置，全局启用LLDP功能和MDN（多设备通知）功能。LLDP用于自动发现网络拓扑，设备通过LLDP可以获取邻居设备的信息，如设备名称、端口标识、系统能力等，便于网络管理和故障排除。//
lldp enable
lldp mdn enable
#
//这是安全Telnet(STelnet)服务器配置，启用STelnet服务器功能。STelnet是Telnet的安全版本，使用SSH协议进行加密，提供安全的远程登录方式，保护管理流量不被窃听或篡改。//
stelnet server enable
#
//这是SSH服务器加密算法配置，设置SSH服务器支持的加密算法为aes256_ctr和aes128_ctr，消息认证码算法为sha2_256_96、sha2_256和sha1_96，密钥交换算法包括多种安全的DH、ECDH和国密SM2算法。这些配置增强了SSH连接的安全性，确保远程管理会话的机密性和完整性。//
ssh server cipher aes256_ctr aes128_ctr
ssh server hmac sha2_256_96 sha2_256 sha1_96
ssh server key-exchange dh_group_exchange_sha256 dh_group_exchange_sha1 ecdh_sha2_nistp256 ecdh_sha2_nistp384 ecdh_sha2_nistp521 sm2_kep
#
//这是SSH服务器DH密钥交换配置，设置DH密钥交换的最小长度为1024位。DH(Diffie-Hellman)密钥交换是SSH协议中用于安全地协商会话密钥的算法，增加密钥长度可以提高安全性，防止中间人攻击和密钥破解。//
ssh server dh-exchange min-len 1024
#
//这是SSH客户端加密算法配置，设置SSH客户端支持的加密算法，包括多种AES算法（GCM模式、CTR模式和CBC模式）以及3DES算法。这些配置确保设备作为SSH客户端时，能够与各种SSH服务器建立安全连接，同时优先使用更安全的加密算法。//
ssh client cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr aes256_cbc aes128_cbc 3des_cbc
#
//这是命令权限配置，设置level 1（最低权限级别）用户在global视图下可以执行display current-configuration命令，在shell视图下可以执行screen-length命令。这些配置允许低权限用户查看设备配置和设置屏幕显示长度，便于基本的设备状态查看，同时不允许其修改配置，提高设备的安全性。//
command-privilege level 1 view global display current-configuration
command-privilege level 1 view shell screen-length
#
//这是用户界面最大数量配置，设置最大虚拟终端(VTY)用户界面数量为21。VTY用户界面用于远程登录设备，如Telnet或SSH连接，增加VTY数量可以支持更多的并发远程管理会话。//
user-interface maximum-vty 21
#
//这是控制台用户界面配置，配置控制台用户界面0使用AAA认证模式。控制台接口是设备的本地管理接口，通过配置AAA认证，要求用户通过AAA服务器的身份验证才能登录设备，提高设备的安全性。//
user-interface con 0
 authentication-mode aaa
#
//这是虚拟终端用户界面配置，配置VTY用户界面0到20使用AAA认证模式，设置用户默认权限级别为3，只允许SSH协议接入。这些配置增强了远程管理的安全性，要求用户通过AAA认证，限制用户权限，并强制使用加密的SSH协议而非明文的Telnet协议。//
user-interface vty 0 20
 authentication-mode aaa
 user privilege level 3
 protocol inbound ssh
#
//这是端口组配置，创建端口组1，将10GE1/0/1、10GE1/0/2、10GE1/0/3和10GE1/0/4四个接口加入该组。端口组用于批量管理和配置多个接口，简化配置操作，提高管理效率。//
port-group 1
 group-member 10GE1/0/1
 group-member 10GE1/0/2
 group-member 10GE1/0/3
 group-member 10GE1/0/4
#
//这是虚拟机管理器配置，启用VM管理器功能。VM管理器用于管理设备上运行的虚拟机，支持网络功能虚拟化(NFV)，允许在网络设备上部署和运行虚拟网络功能。//
vm-manager
#
//这是VXLAN隧道状态跟踪配置，设置VXLAN隧道状态跟踪模式为精确路由。该配置使VXLAN隧道状态与底层路由状态紧密关联，当路由发生变化时，VXLAN隧道状态也会相应调整，提高VXLAN网络的可靠性和收敛速度。//
vxlan tunnel-status track exact-route
#
//这是配置结束标记，表示配置文件的结束。return命令用于从系统视图返回到用户视图，在配置文件中表示配置的结束。//
return

