#
clock timezone BJ add 08:00:00
#
sysname SW
#
dfs-group 1
 priority 150
 source ip *********** 
#
info-center source default channel 2 log level warning
info-center loghost source LoopBack0
info-center loghost ************
#
system resource large-route
#
device board 1 board-type CE6855-48S6Q-HI
#
drop-profile default
#
dcb pfc
#
dcb ets-profile default
#
ntp ipv6 server disable
ntp source-interface LoopBack0
ntp unicast-server ********** preferred
ntp unicast-server **********
#
assign forward layer-3 resource large-overlay
#
arp resource-saving-mode
#
assign forward ipv6 longer-mask resource 1024
#
vlan reserved for main-interface 4060 to 4063
#
mac-address flapping periodical trap enable
#
stp mode rstp
stp v-stp enable
#
evpn-overlay enable
#
lacp m-lag system-id 00e0-fc00-0001
#
telnet server disable
telnet ipv6 server disable
#
diffserv domain default
#
ip vpn-instance ManageOne_0000011
 ipv4-family
  route-distinguisher 78:50017
  vpn-target 0:50017 export-extcommunity
  vpn-target 0:50017 export-extcommunity evpn
  vpn-target 0:50017 import-extcommunity
  vpn-target 0:50017 import-extcommunity evpn
 description ManageOne(0c7f69422055424a8734d71ce7a44979)-fusionsphere_ManageOne
 vxlan vni 50017
#
ip vpn-instance VPC-wlzys-APP_0000013
 ipv4-family
  route-distinguisher 78:50019
  vpn-target 0:50019 export-extcommunity
  vpn-target 0:50019 export-extcommunity evpn
  vpn-target 0:50019 import-extcommunity
  vpn-target 0:50019 import-extcommunity evpn
 description weiwo(9f52ee18f3bb41e3825280792429a1b1)-fusionsphere_VPC-wlzys-APP
 ipv6-family 
  route-distinguisher 78:50019
  vpn-target 0:50019 export-extcommunity
  vpn-target 0:50019 export-extcommunity evpn
  vpn-target 0:50019 import-extcommunity
  vpn-target 0:50019 import-extcommunity evpn
 vxlan vni 50019
#
sdn agent
 controller-ip ************
  openflow agent
   transport-address ***********
 controller-ip ************
  openflow agent
   transport-address ***********
#
bridge-domain 31
 vxlan vni 5037
 evpn
  route-distinguisher 78:5037
  vpn-target 0:5037 export-extcommunity
  vpn-target 0:5037 import-extcommunity
#
acl number 2222
 description SNMP
 rule 5 permit source *********** ********
#
acl number 3333
 description vty
 rule 5 permit ip source ********** ***********
 rule 10 permit ip source ************* *********
 rule 15 permit ip source ************ *********
 rule 20 permit ip source ************ *********
 rule 25 permit ip source ************ *********
 rule 30 permit ip source ************ 0
 rule 35 permit ip vpn-instance mgmt source ********** ***********
 rule 40 permit ip vpn-instance mgmt source ************* *********
 rule 45 permit ip vpn-instance mgmt source ************ *********
 rule 50 permit ip vpn-instance mgmt source ************ *********
 rule 55 permit ip vpn-instance mgmt source ************ *********
 rule 60 permit ip vpn-instance mgmt source ************ 0
 rule 65 permit ip vpn-instance mgmt source *********** ********
 rule 70 permit ip source *********** ********
#

 authentication-scheme default
 #
 authorization-scheme default
 #
 accounting-scheme default
 #
 domain default
 #
 domain default_admin
#
stack
#
license
#
interface Vbdif1139
 ip binding vpn-instance VPC_jsAIzx_DB1_50513
 ip address ************ *************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif1143
 ip binding vpn-instance VPC_wlcpyfs_APP_50261
 ipv6 enable
 ip address ************** ***************
 ipv6 address 2408:81B0:A00:1:1500:10:0:1/96
 ipv6 nd ra prefix 2408:81B0:A00:1:1500:10::/96 0 0 no-autoconfig off-link
 mac-address 0000-5e00-0102
 ipv6 nd ra halt disable
 ipv6 nd autoconfig managed-address-flag
 ipv6 nd autoconfig other-flag
 ipv6 nd collect host enable
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif1149
 ip binding vpn-instance VPC_wlcpyfs_DB_50262
 ip address ************** ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable

interface MEth0/0/0
 ip binding vpn-instance mgmt
 ip address *********** *************
#
interface Eth-Trunk0
 description "To-[ZY1B3F1-05P12L44U-Pub-HW6855-YWLF01]-eth-trunk0"
 mode lacp-static
 peer-link 1
#
interface Eth-Trunk11
 undo portswitch
 description To-[ZY1B3F1-06P15L03U-Pub-HW12816-YWSP01]-eth-trunk95
 ip address *********** ***************
 ospf peer hold-max-cost timer 800000
 mode lacp-static
#
interface Eth-Trunk61
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 61
 port nvo3 mode access
#
interface Eth-Trunk61.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk95
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 95
 port nvo3 mode access
#
interface Eth-Trunk95.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk95.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 424
#
interface Eth-Trunk95.3 mode l2
 encapsulation dot1q vid 101
 bridge-domain 1149
#
interface Eth-Trunk95.6 mode l2
 encapsulation dot1q vid 108
 bridge-domain 895
#
interface Eth-Trunk95.7 mode l2
 encapsulation dot1q vid 112
 bridge-domain 53
#
interface Eth-Trunk95.11 mode l2
 encapsulation dot1q vid 109
 bridge-domain 263
#
interface Eth-Trunk95.12 mode l2
 encapsulation dot1q vid 110
 bridge-domain 262
#
interface Eth-Trunk95.13 mode l2
 encapsulation dot1q vid 111
 bridge-domain 103
#
interface Eth-Trunk95.15 mode l2
 encapsulation dot1q vid 113
 bridge-domain 166
#
interface Eth-Trunk95.16 mode l2
 encapsulation dot1q vid 114
 bridge-domain 72
#
interface 10GE1/0/1
 description "To-[ZY1B3F1-05P12L04U-XNH-LC3-SVR01]-TBD2"
 eth-trunk 61
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 40GE1/0/6
 description "To-[ZY1B3F1-05P12L44U-Pub-HW6855-YWLF01]-40GE1/0/6"
 eth-trunk 0
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 40GBASE-FIBER
#
interface LoopBack0
 ip address *********** ***************
#
interface LoopBack1
 ipv6 enable
 ip address *********** ***************
#
interface Nve1
 source *******
 vni 5037 head-end peer-list protocol bgp
 vni 6007 head-end peer-list protocol bgp
 vni 6010 head-end peer-list protocol bgp
 mac-address 0000-5e00-0135
#
interface NULL0
#
monitor-link group 1
 port 40GE1/0/1 uplink
 port 40GE1/0/2 uplink
 port 10GE1/0/47 downlink 47
 port 10GE1/0/48 downlink 48
 timer recover-time 60
#
bgp 100
 router-id ***********
 peer ********** as-number 100
 peer ********** connect-interface LoopBack0
 peer ********** as-number 100
 peer ********** connect-interface LoopBack0
 peer ********** as-number 100
 peer ********** connect-interface LoopBack0
 peer ********** as-number 100
 peer ********** connect-interface LoopBack0
 #
 ipv4-family unicast
  peer ********** enable
  peer ********** enable
  peer ********** enable
  peer ********** enable
 #
 ipv4-family vpn-instance ManageOne_0000011
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance VPC-wlzys-APP_0000013
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv6-family vpn-instance VPC-wlzys-APP_0000013
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv6-family vpn-instance VPC_NMS_JT_0000089
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 l2vpn-family evpn
  policy vpn-target
  peer ********** enable
  peer ********** advertise irb
  peer ********** advertise irbv6
  peer ********** enable
  peer ********** advertise irb
  peer ********** advertise irbv6
  peer ********** enable
  peer ********** advertise irb
  peer ********** advertise irbv6
  peer ********** enable
  peer ********** advertise irb
  peer ********** advertise irbv6
#
ospf 10 router-id ***********
 spf-schedule-interval intelligent-timer 50 50 50
 lsa-originate-interval intelligent-timer 50 50 50
 lsa-arrival-interval intelligent-timer 50 50 50
 area 0.0.0.0
  network *********** 0.0.0.0
  network *********** 0.0.0.0
  network *********** *******
  network *********** *******
  network *********** *******
  network *********** *******
#
ip route-static vpn-instance mgmt 0.0.0.0 0.0.0.0 MEth0/0/0 ************ description mgmt
#
snmp-agent
snmp-agent local-engineid 800007DB039C741A50A2C1
snmp-agent community read cipher %^%#a}-Q%WL5d6agCD8Y^+WQQ/%#"}deTO^.{1#XOO"T_0kt6i4&}*!TI!=LKQ(7iOw+B("`h+;>{ZH<t;d-%^%# acl 2222
#
snmp-agent sys-info version all
snmp-agent group v3 dc-admin privacy read-view rd write-view wt notify-view nt
snmp-agent target-host trap address udp-domain ************ params securityname cipher %^%#w/5&T2u"t5DI3>PgpX^E.ZS7Xv06&*V1(.WuY|YM%^%#
snmp-agent target-host trap address udp-domain *********** params securityname cipher %^%#ACw70["Y|C$>TkG=Yp;(q1\3+Xv`Y84qVWU[0~2K%^%#
#
snmp-agent mib-view included nt iso
snmp-agent mib-view included rd iso
snmp-agent mib-view included wt iso
snmp-agent mib-view included iso-view iso
snmp-agent usm-user v3 admin
snmp-agent usm-user v3 uhmroot
snmp-agent usm-user v3 uhmroot group dc-admin
snmp-agent usm-user v3 uhmroot authentication-mode sha cipher %^%#[t9u%9i*fIb6,bSnc:.)4CND9&^ct9>:#ZYuN9C9%^%#
snmp-agent usm-user v3 uhmroot privacy-mode aes128 cipher %^%#|aY(B5g)}L#AyBWW)KnBVM5F.8r%o~Q{TWW/sEEC%^%#
#
snmp-agent trap source LoopBack0
#
snmp-agent trap enable
#
lldp enable
lldp mdn enable
#
stelnet server enable
#
ssh server cipher aes256_ctr aes128_ctr
ssh server hmac sha2_256_96 sha2_256 sha1_96
ssh server key-exchange dh_group_exchange_sha256 dh_group_exchange_sha1 ecdh_sha2_nistp256 ecdh_sha2_nistp384 ecdh_sha2_nistp521 sm2_kep
#
ssh server dh-exchange min-len 1024
#
ssh client cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr aes256_cbc aes128_cbc 3des_cbc
#
command-privilege level 1 view global display current-configuration
command-privilege level 1 view shell screen-length
#
user-interface maximum-vty 21
#
user-interface con 0
 authentication-mode aaa
#
user-interface vty 0 20
 authentication-mode aaa
 user privilege level 3
 protocol inbound ssh
#
port-group 1
 group-member 10GE1/0/1
 group-member 10GE1/0/2
 group-member 10GE1/0/3
 group-member 10GE1/0/4
#
vm-manager
#
vxlan tunnel-status track exact-route
#
return

