你是专业的网络拓扑图设计师，你可以熟练使用visio、draw.io或者PowerPoint等绘图工具，
请根据以下网络配置，生成对应拓扑图，格式要求是html：
router>display cur
!Software Version V800R022C00SPC600
!Last configuration was updated at 2025-01-14 10:21:44+08:00 by n2eq708p
!Last configuration was saved at 2025-01-08 22:16:50+08:00 by n2eq708p
#
mpls ldp-srbe convergence enhance
#
clock timezone BJ add 08:00:00
#
sysname router
#
set neid 4ce631
#
undo FTP server-source all-interface
undo FTP ipv6 server-source all-interface
#
info-center source cli channel 2 log level notification
info-center source ssh channel 2 trap level informational
info-center source default channel 2 log level warning trap level warning
info-center source ssh channel 4 log level informational
info-center loghost source GigabitEthernet0/0/0
info-center loghost ************ vpn-instance mgt
#
set service-mode forwarding-mode compatible
#
dustproof check-timer day 0
#
service-template template-default0
#
service-template template-default1
#
service-template template-default2
#
service-template template-default3
#
service-template template-default4
#
ntp-service server disable
ntp-service ipv6 server disable
ntp-service server source-interface all disable
ntp-service ipv6 server source-interface all disable
ntp-service unicast-server ************ vpn-instance mgt
ntp-service authentication enable
#
undo dhcp enable
#
undo icmp name timestamp-reply send
#
router id *************
#
undo telnet server enable
undo telnet ipv6 server enable
undo telnet server-source all-interface
undo telnet ipv6 server-source all-interface
#
diffserv domain default
#
diffserv domain 5p3d
#
soc
#
ip vpn-instance 5G-TF_A
 ipv4-family
  route-distinguisher 65159:114
  apply-label per-instance
  vpn-target 65159:114 export-extcommunity
  vpn-target 65159:114 import-extcommunity
 ipv6-family
  route-distinguisher 65159:114
  apply-label per-instance
  vpn-target 65159:114 export-extcommunity
  vpn-target 65159:114 import-extcommunity
#
ip vpn-instance 5GC_MGMN_B
 ipv4-family
  route-distinguisher 65159:227
  apply-label per-instance
  vpn-target 65159:227 export-extcommunity
  vpn-target 65159:227 import-extcommunity
 ipv6-family
  route-distinguisher 65159:227
  apply-label per-instance
  vpn-target 65159:227 export-extcommunity
  vpn-target 65159:227 import-extcommunity
#
ip vpn-instance 5GC_NEF_B
 ipv4-family
  route-distinguisher 65159:232
  apply-label per-instance
  vpn-target 65159:232 export-extcommunity
  vpn-target 65159:232 import-extcommunity
 ipv6-family
  route-distinguisher 65159:232
  apply-label per-instance
  vpn-target 65159:232 export-extcommunity
  vpn-target 65159:232 import-extcommunity
#
ip vpn-instance 5GC_TANZHEN_B
 ipv4-family
  route-distinguisher 65159:237
  apply-label per-instance
  vpn-target 65159:237 export-extcommunity
  vpn-target 65159:237 import-extcommunity
 ipv6-family
  route-distinguisher 65159:237
  apply-label per-instance
  vpn-target 65159:237 export-extcommunity
  vpn-target 65159:237 import-extcommunity
#
ip vpn-instance 5GC_Test_B
 ipv4-family
  route-distinguisher 65159:239
  apply-label per-instance
  vpn-target 65159:239 export-extcommunity
  vpn-target 65159:239 import-extcommunity
 ipv6-family
  route-distinguisher 65159:239
  apply-label per-instance
  vpn-target 65159:239 export-extcommunity
  vpn-target 65159:239 import-extcommunity
#
ip vpn-instance 5G_MEC_B
 ipv4-family
  route-distinguisher 65159:228
  apply-label per-instance
  vpn-target 65159:228 export-extcommunity
  vpn-target 65159:228 import-extcommunity
 ipv6-family
  route-distinguisher 65159:228
  apply-label per-instance
  vpn-target 65159:228 export-extcommunity
  vpn-target 65159:228 import-extcommunity
#
ip vpn-instance 5G_RCS_B
 ipv4-family
  route-distinguisher 65159:233
  apply-label per-instance
  vpn-target 65159:233 export-extcommunity
  vpn-target 65159:233 import-extcommunity
 ipv6-family
  route-distinguisher 65159:233
  apply-label per-instance
  vpn-target 65159:233 export-extcommunity
  vpn-target 65159:233 import-extcommunity
#
ip vpn-instance BSS-EW_A
 ipv4-family
  route-distinguisher 65159:110
  apply-label per-instance
  vpn-target 65159:110 export-extcommunity
  vpn-target 65159:110 import-extcommunity
 ipv6-family
  route-distinguisher 65159:110
  apply-label per-instance
  vpn-target 65159:110 export-extcommunity
  vpn-target 65159:110 import-extcommunity
#
ip vpn-instance BSS-SN_A
 ipv4-family
  route-distinguisher 65159:101
  apply-label per-instance
  vpn-target 65159:101 export-extcommunity
  vpn-target 65159:101 import-extcommunity
 ipv6-family
  route-distinguisher 65159:101
  apply-label per-instance
  vpn-target 65159:101 export-extcommunity
  vpn-target 65159:101 import-extcommunity
#
ip vpn-instance CDN_MGMN-H_A
 ipv4-family
  route-distinguisher 65159:242
  apply-label per-instance
  vpn-target 65159:242 export-extcommunity
  vpn-target 65159:242 import-extcommunity
#
ip vpn-instance CN_MGMN_B
 ipv4-family
  route-distinguisher 65159:231
  apply-label per-instance
  vpn-target 65159:231 export-extcommunity
  vpn-target 65159:231 import-extcommunity
 ipv6-family
  route-distinguisher 65159:231
  apply-label per-instance
  vpn-target 65159:231 export-extcommunity
  vpn-target 65159:231 import-extcommunity
#
ip vpn-instance CU_OTNCPE_SDN_B
 ipv4-family
  route-distinguisher 65159:236
  apply-label per-instance
  vpn-target 65159:236 export-extcommunity
  vpn-target 65159:236 import-extcommunity
 ipv6-family
  route-distinguisher 65159:236
  apply-label per-instance
  vpn-target 65159:236 export-extcommunity
  vpn-target 65159:236 import-extcommunity
#
ip vpn-instance DCI-CU_ZWZX-9_A
 ipv4-family
  route-distinguisher 65159:105
  apply-label per-instance
  vpn-target 65159:105 export-extcommunity
  vpn-target 65159:105 import-extcommunity
 ipv6-family
  route-distinguisher 65159:105
  apply-label per-instance
  vpn-target 65159:105 export-extcommunity
  vpn-target 65159:105 import-extcommunity
#
ip vpn-instance GLW_BVMP-H_A
 ipv4-family
  route-distinguisher 65159:241
  apply-label per-instance
  vpn-target 65159:241 export-extcommunity
  vpn-target 65159:241 import-extcommunity
 ipv6-family
  route-distinguisher 65159:241
  apply-label per-instance
  vpn-target 65159:241 export-extcommunity
  vpn-target 65159:241 import-extcommunity
#
ip vpn-instance HW_WDM_NMS_JT_B
 ipv4-family
  route-distinguisher 65159:240
  apply-label per-instance
  vpn-target 65159:240 export-extcommunity
  vpn-target 65159:240 import-extcommunity
#
ip vpn-instance IDC_CONTROL-H_A
 ipv4-family
  route-distinguisher 65159:108
  apply-label per-instance
  vpn-target 65159:108 export-extcommunity
  vpn-target 65159:108 import-extcommunity
 ipv6-family
  route-distinguisher 65159:108
  apply-label per-instance
  vpn-target 65159:108 export-extcommunity
  vpn-target 65159:108 import-extcommunity
#
ip vpn-instance IMS_B
 ipv4-family
  route-distinguisher 65159:201
  apply-label per-instance
  vpn-target 65159:201 export-extcommunity
  vpn-target 65159:201 import-extcommunity
 ipv6-family
  route-distinguisher 65159:201
  apply-label per-instance
  vpn-target 65159:201 export-extcommunity
  vpn-target 65159:201 import-extcommunity
#
ip vpn-instance IOTPT_ZW_B
 ipv4-family
  route-distinguisher 65159:230
  apply-label per-instance
  vpn-target 65159:230 export-extcommunity
  vpn-target 65159:230 import-extcommunity
 ipv6-family
  route-distinguisher 65159:230
  apply-label per-instance
  vpn-target 65159:230 export-extcommunity
  vpn-target 65159:230 import-extcommunity
#
ip vpn-instance IOT_BSS_B
 ipv4-family
  route-distinguisher 65159:224
  apply-label per-instance
  vpn-target 65159:224 export-extcommunity
  vpn-target 65159:224 import-extcommunity
 ipv6-family
  route-distinguisher 65159:224
  apply-label per-instance
  vpn-target 65159:224 export-extcommunity
  vpn-target 65159:224 import-extcommunity
#
ip vpn-instance IPRAN_JT_B
 ipv4-family
  route-distinguisher 65159:223
  apply-label per-instance
  vpn-target 65159:223 export-extcommunity
  vpn-target 65159:223 import-extcommunity
 ipv6-family
  route-distinguisher 65159:223
  apply-label per-instance
  vpn-target 65159:223 export-extcommunity
  vpn-target 65159:223 import-extcommunity
#
ip vpn-instance IPRAN_NMS_JT_A
 ipv4-family
  route-distinguisher 65159:109
  apply-label per-instance
  vpn-target 65159:109 export-extcommunity
  vpn-target 65159:109 import-extcommunity
 ipv6-family
  route-distinguisher 65159:109
  apply-label per-instance
  vpn-target 65159:109 export-extcommunity
  vpn-target 65159:109 import-extcommunity
#
ip vpn-instance IPRAN_NMS_JT_B
 ipv4-family
  route-distinguisher 65159:229
  apply-label per-instance
  vpn-target 65159:229 export-extcommunity
  vpn-target 65159:229 import-extcommunity
 ipv6-family
  route-distinguisher 65159:229
  apply-label per-instance
  vpn-target 65159:229 export-extcommunity
  vpn-target 65159:229 import-extcommunity
#
ip vpn-instance IoT_OSS_B
 ipv4-family
  route-distinguisher 65159:225
  apply-label per-instance
  vpn-target 65159:225 export-extcommunity
  vpn-target 65159:225 import-extcommunity
 ipv6-family
  route-distinguisher 65159:225
  apply-label per-instance
  vpn-target 65159:225 export-extcommunity
  vpn-target 65159:225 import-extcommunity
#
ip vpn-instance MSS-EW_A
 ipv4-family
  route-distinguisher 65159:112
  apply-label per-instance
  vpn-target 65159:112 export-extcommunity
  vpn-target 65159:112 import-extcommunity
 ipv6-family
  route-distinguisher 65159:112
  apply-label per-instance
  vpn-target 65159:112 export-extcommunity
  vpn-target 65159:112 import-extcommunity
#
ip vpn-instance MSS-SN_A
 ipv4-family
  route-distinguisher 65159:115
  apply-label per-instance
  vpn-target 65159:115 export-extcommunity
  vpn-target 65159:115 import-extcommunity
 ipv6-family
  route-distinguisher 65159:115
  apply-label per-instance
  vpn-target 65159:115 export-extcommunity
  vpn-target 65159:115 import-extcommunity
#
ip vpn-instance MVSP_NMS_B
 ipv4-family
  route-distinguisher 65159:221
  apply-label per-instance
  vpn-target 65159:221 export-extcommunity
  vpn-target 65159:221 import-extcommunity
 ipv6-family
  route-distinguisher 65159:221
  apply-label per-instance
  vpn-target 65159:221 export-extcommunity
  vpn-target 65159:221 import-extcommunity
#
ip vpn-instance MVSP_VS_B
 ipv4-family
  route-distinguisher 65159:220
  apply-label per-instance
  vpn-target 65159:220 export-extcommunity
  vpn-target 65159:220 import-extcommunity
 ipv6-family
  route-distinguisher 65159:220
  apply-label per-instance
  vpn-target 65159:220 export-extcommunity
  vpn-target 65159:220 import-extcommunity
#
ip vpn-instance NCRP_ZZRP_A
 ipv4-family
  route-distinguisher 65159:116
  apply-label per-instance
  vpn-target 65159:116 export-extcommunity
  vpn-target 65159:116 import-extcommunity
 ipv6-family
  route-distinguisher 65159:116
  apply-label per-instance
  vpn-target 65159:116 export-extcommunity
  vpn-target 65159:116 import-extcommunity
#
ip vpn-instance NKFVPN_B
 ipv4-family
  route-distinguisher 65159:202
  apply-label per-instance
  vpn-target 65159:202 export-extcommunity
  vpn-target 65159:202 import-extcommunity
 ipv6-family
  route-distinguisher 65159:202
  apply-label per-instance
  vpn-target 65159:202 export-extcommunity
  vpn-target 65159:202 import-extcommunity
#
ip vpn-instance OSS-EW_A
 ipv4-family
  route-distinguisher 65159:111
  apply-label per-instance
  vpn-target 65159:111 export-extcommunity
  vpn-target 65159:111 import-extcommunity
 ipv6-family
  route-distinguisher 65159:111
  apply-label per-instance
  vpn-target 65159:111 export-extcommunity
  vpn-target 65159:111 import-extcommunity
#
ip vpn-instance OSS-SN_A
 ipv4-family
  route-distinguisher 65159:113
  apply-label per-instance
  vpn-target 65159:113 export-extcommunity
  vpn-target 65159:113 import-extcommunity
 ipv6-family
  route-distinguisher 65159:113
  apply-label per-instance
  vpn-target 65159:113 export-extcommunity
  vpn-target 65159:113 import-extcommunity
#
ip vpn-instance OSS_JT_B
 ipv4-family
  route-distinguisher 65159:222
  apply-label per-instance
  vpn-target 65159:222 export-extcommunity
  vpn-target 65159:222 import-extcommunity
 ipv6-family
  route-distinguisher 65159:222
  apply-label per-instance
  vpn-target 65159:222 export-extcommunity
  vpn-target 65159:222 import-extcommunity
#
ip vpn-instance OTN_NMS_JT_B
 ipv4-family
  route-distinguisher 65159:226
  apply-label per-instance
  vpn-target 65159:226 export-extcommunity
  vpn-target 65159:226 import-extcommunity
 ipv6-family
  route-distinguisher 65159:226
  apply-label per-instance
  vpn-target 65159:226 export-extcommunity
  vpn-target 65159:226 import-extcommunity
#
ip vpn-instance SDONT_CQ_ZX_B
 ipv4-family
  route-distinguisher 65159:205
  apply-label per-instance
  vpn-target 65159:205 export-extcommunity
  vpn-target 65159:205 import-extcommunity
 ipv6-family
  route-distinguisher 65159:205
  apply-label per-instance
  vpn-target 65159:205 export-extcommunity
  vpn-target 65159:205 import-extcommunity
#
ip vpn-instance SDOTN_AH_ZX_B
 ipv4-family
  route-distinguisher 65159:203
  apply-label per-instance
  vpn-target 65159:203 export-extcommunity
  vpn-target 65159:203 import-extcommunity
 ipv6-family
  route-distinguisher 65159:203
  apply-label per-instance
  vpn-target 65159:203 export-extcommunity
  vpn-target 65159:203 import-extcommunity
#
ip vpn-instance SDOTN_FJ_ZX_B
 ipv4-family
  route-distinguisher 65159:204
  apply-label per-instance
  vpn-target 65159:204 export-extcommunity
  vpn-target 65159:204 import-extcommunity
 ipv6-family
  route-distinguisher 65159:204
  apply-label per-instance
  vpn-target 65159:204 export-extcommunity
  vpn-target 65159:204 import-extcommunity
#
ip vpn-instance SDOTN_GS_HW_B
 ipv4-family
  route-distinguisher 65159:216
  apply-label per-instance
  vpn-target 65159:216 export-extcommunity
  vpn-target 65159:216 import-extcommunity
 ipv6-family
  route-distinguisher 65159:216
  apply-label per-instance
  vpn-target 65159:216 export-extcommunity
  vpn-target 65159:216 import-extcommunity
#
ip vpn-instance SDOTN_GX_ZX_B
 ipv4-family
  route-distinguisher 65159:213
  apply-label per-instance
  vpn-target 65159:213 export-extcommunity
  vpn-target 65159:213 import-extcommunity
 ipv6-family
  route-distinguisher 65159:213
  apply-label per-instance
  vpn-target 65159:213 export-extcommunity
  vpn-target 65159:213 import-extcommunity
#
ip vpn-instance SDOTN_HB_ZX_B
 ipv4-family
  route-distinguisher 65159:207
  apply-label per-instance
  vpn-target 65159:207 export-extcommunity
  vpn-target 65159:207 import-extcommunity
 ipv6-family
  route-distinguisher 65159:207
  apply-label per-instance
  vpn-target 65159:207 export-extcommunity
  vpn-target 65159:207 import-extcommunity
#
ip vpn-instance SDOTN_HEN_ZX_B
 ipv4-family
  route-distinguisher 65159:208
  apply-label per-instance
  vpn-target 65159:208 export-extcommunity
  vpn-target 65159:208 import-extcommunity
 ipv6-family
  route-distinguisher 65159:208
  apply-label per-instance
  vpn-target 65159:208 export-extcommunity
  vpn-target 65159:208 import-extcommunity
#
ip vpn-instance SDOTN_HN_ZX_B
 ipv4-family
  route-distinguisher 65159:212
  apply-label per-instance
  vpn-target 65159:212 export-extcommunity
  vpn-target 65159:212 import-extcommunity
 ipv6-family
  route-distinguisher 65159:212
  apply-label per-instance
  vpn-target 65159:212 export-extcommunity
  vpn-target 65159:212 import-extcommunity
#
ip vpn-instance SDOTN_JT_B
 ipv4-family
  route-distinguisher 65159:235
  apply-label per-instance
  vpn-target 65159:235 export-extcommunity
  vpn-target 65159:235 import-extcommunity
 ipv6-family
  route-distinguisher 65159:235
  apply-label per-instance
  vpn-target 65159:235 export-extcommunity
  vpn-target 65159:235 import-extcommunity
#
ip vpn-instance SDOTN_JX_HW_B
 ipv4-family
  route-distinguisher 65159:217
  apply-label per-instance
  vpn-target 65159:217 export-extcommunity
  vpn-target 65159:217 import-extcommunity
 ipv6-family
  route-distinguisher 65159:217
  apply-label per-instance
  vpn-target 65159:217 export-extcommunity
  vpn-target 65159:217 import-extcommunity
#
ip vpn-instance SDOTN_JX_ZX_B
 ipv4-family
  route-distinguisher 65159:209
  apply-label per-instance
  vpn-target 65159:209 export-extcommunity
  vpn-target 65159:209 import-extcommunity
 ipv6-family
  route-distinguisher 65159:209
  apply-label per-instance
  vpn-target 65159:209 export-extcommunity
  vpn-target 65159:209 import-extcommunity
#
ip vpn-instance SDOTN_LN_ZX_B
 ipv4-family
  route-distinguisher 65159:211
  apply-label per-instance
  vpn-target 65159:211 export-extcommunity
  vpn-target 65159:211 import-extcommunity
 ipv6-family
  route-distinguisher 65159:211
  apply-label per-instance
  vpn-target 65159:211 export-extcommunity
  vpn-target 65159:211 import-extcommunity
#
ip vpn-instance SDOTN_SC_ZX_B
 ipv4-family
  route-distinguisher 65159:214
  apply-label per-instance
  vpn-target 65159:214 export-extcommunity
  vpn-target 65159:214 import-extcommunity
 ipv6-family
  route-distinguisher 65159:214
  apply-label per-instance
  vpn-target 65159:214 export-extcommunity
  vpn-target 65159:214 import-extcommunity
#
ip vpn-instance SDOTN_SX_ZX_B
 ipv4-family
  route-distinguisher 65159:210
  apply-label per-instance
  vpn-target 65159:210 export-extcommunity
  vpn-target 65159:210 import-extcommunity
 ipv6-family
  route-distinguisher 65159:210
  apply-label per-instance
  vpn-target 65159:210 export-extcommunity
  vpn-target 65159:210 import-extcommunity
#
ip vpn-instance SDOTN_TEMP_B
 ipv4-family
  route-distinguisher 65159:238
  apply-label per-instance
  vpn-target 65159:238 export-extcommunity
  vpn-target 65159:238 import-extcommunity
 ipv6-family
  route-distinguisher 65159:238
  apply-label per-instance
  vpn-target 65159:238 export-extcommunity
  vpn-target 65159:238 import-extcommunity
#
ip vpn-instance SDOTN_XZ_HW_B
 ipv4-family
  route-distinguisher 65159:219
  apply-label per-instance
  vpn-target 65159:219 export-extcommunity
  vpn-target 65159:219 import-extcommunity
 ipv6-family
  route-distinguisher 65159:219
  apply-label per-instance
  vpn-target 65159:219 export-extcommunity
  vpn-target 65159:219 import-extcommunity
#
ip vpn-instance SDOTN_XZ_ZX_B
 ipv4-family
  route-distinguisher 65159:206
  apply-label per-instance
  vpn-target 65159:206 export-extcommunity
  vpn-target 65159:206 import-extcommunity
 ipv6-family
  route-distinguisher 65159:206
  apply-label per-instance
  vpn-target 65159:206 export-extcommunity
  vpn-target 65159:206 import-extcommunity
#
ip vpn-instance SDOTN_YN_HW_B
 ipv4-family
  route-distinguisher 65159:218
  apply-label per-instance
  vpn-target 65159:218 export-extcommunity
  vpn-target 65159:218 import-extcommunity
 ipv6-family
  route-distinguisher 65159:218
  apply-label per-instance
  vpn-target 65159:218 export-extcommunity
  vpn-target 65159:218 import-extcommunity
#
ip vpn-instance SDOTN_YN_ZX_B
 ipv4-family
  route-distinguisher 65159:215
  apply-label per-instance
  vpn-target 65159:215 export-extcommunity
  vpn-target 65159:215 import-extcommunity
 ipv6-family
  route-distinguisher 65159:215
  apply-label per-instance
  vpn-target 65159:215 export-extcommunity
  vpn-target 65159:215 import-extcommunity
#
ip vpn-instance SMAN_NMS_CJ_A
 ipv4-family
  route-distinguisher 65159:104
  apply-label per-instance
  vpn-target 65159:104 export-extcommunity
  vpn-target 65159:104 import-extcommunity
 ipv6-family
  route-distinguisher 65159:104
  apply-label per-instance
  vpn-target 65159:104 export-extcommunity
  vpn-target 65159:104 import-extcommunity
#
ip vpn-instance SMAN_NMS_JT_A
 ipv4-family
  route-distinguisher 65159:103
  apply-label per-instance
  vpn-target 65159:103 export-extcommunity
  vpn-target 65159:103 import-extcommunity
 ipv6-family
  route-distinguisher 65159:103
  apply-label per-instance
  vpn-target 65159:103 export-extcommunity
  vpn-target 65159:103 import-extcommunity
#
ip vpn-instance TAC_DATA_A
 ipv4-family
  route-distinguisher 65159:102
  apply-label per-instance
  vpn-target 65159:102 export-extcommunity
  vpn-target 65159:102 import-extcommunity
 ipv6-family
  route-distinguisher 65159:102
  apply-label per-instance
  vpn-target 65159:102 export-extcommunity
  vpn-target 65159:102 import-extcommunity
#
ip vpn-instance TAC_VSS_A
 ipv4-family
  route-distinguisher 65159:119
  apply-label per-instance
  vpn-target 65159:119 export-extcommunity
  vpn-target 65159:119 import-extcommunity
 ipv6-family
  route-distinguisher 65159:119
  apply-label per-instance
  vpn-target 65159:119 export-extcommunity
  vpn-target 65159:119 import-extcommunity
#
ip vpn-instance UTC_MGMN_B
 ipv4-family
  route-distinguisher 65159:234
  apply-label per-instance
  vpn-target 65159:234 export-extcommunity
  vpn-target 65159:234 import-extcommunity
 ipv6-family
  route-distinguisher 65159:234
  apply-label per-instance
  vpn-target 65159:234 export-extcommunity
  vpn-target 65159:234 import-extcommunity
#
ip vpn-instance VRM-H_A
 ipv4-family
  route-distinguisher 65159:118
  apply-label per-instance
  vpn-target 65159:118 export-extcommunity
  vpn-target 65159:118 import-extcommunity
 ipv6-family
  route-distinguisher 65159:118
  apply-label per-instance
  vpn-target 65159:118 export-extcommunity
  vpn-target 65159:118 import-extcommunity
#
ip vpn-instance ZNZX-NMS_A
 ipv4-family
  route-distinguisher 65159:106
  apply-label per-instance
  vpn-target 65159:106 export-extcommunity
  vpn-target 65159:106 import-extcommunity
 ipv6-family
  route-distinguisher 65159:106
  apply-label per-instance
  vpn-target 65159:106 export-extcommunity
  vpn-target 65159:106 import-extcommunity
#
ip vpn-instance ZNZX_A
 ipv4-family
  route-distinguisher 65159:107
  apply-label per-instance
  vpn-target 65159:107 export-extcommunity
  vpn-target 65159:107 import-extcommunity
 ipv6-family
  route-distinguisher 65159:107
  apply-label per-instance
  vpn-target 65159:107 export-extcommunity
  vpn-target 65159:107 import-extcommunity
#
ip vpn-instance ha_dhtsgz_A
 ipv4-family
  route-distinguisher 65159:117
  apply-label per-instance
  vpn-target 65159:117 export-extcommunity
  vpn-target 65159:117 import-extcommunity
 ipv6-family
  route-distinguisher 65159:117
  apply-label per-instance
  vpn-target 65159:117 export-extcommunity
  vpn-target 65159:117 import-extcommunity
#
ip vpn-instance mgt
 ipv4-family
#
ip vpn-instance vrf_169
 ipv4-family
  route-distinguisher 65159:301
  apply-label per-instance
  vpn-target 65159:301 export-extcommunity
  vpn-target 65159:301 import-extcommunity
 ipv6-family
  route-distinguisher 65159:301
  apply-label per-instance
  vpn-target 65159:301 export-extcommunity
  vpn-target 65159:301 import-extcommunity
#
ip vpn-instance vrf_406
 ipv4-family
  route-distinguisher 65159:401
  apply-label per-instance
  vpn-target 65159:401 export-extcommunity
  vpn-target 65159:401 import-extcommunity
 ipv6-family
  route-distinguisher 65159:401
  apply-label per-instance
  vpn-target 65159:401 export-extcommunity
  vpn-target 65159:401 import-extcommunity
#
undo radius local-ip all
#
bfd
#
mpls lsr-id *************
#
mpls
#
mpls ldp
 #
 ipv4-family
#
dhcp server request-packet all-interface disable
#
dot1x-template 1
#
acl number 2222
 description SNMP
 rule 5 permit vpn-instance mgt source ************ **********
 rule 10 permit vpn-instance mgt source ************* 0
 rule 15 permit vpn-instance mgt source *********** 0
#
acl number 3000
 rule 5 deny tcp destination-port eq 9995
 rule 10 deny tcp destination-port eq 445
 rule 15 deny udp destination-port eq 445
 rule 20 deny udp destination-port eq netbios-ns
 rule 25 deny udp destination-port eq netbios-dgm
 rule 30 deny tcp destination-port eq 9996
 rule 35 deny udp destination-port eq tftp
 rule 40 deny tcp destination-port eq 4444
 rule 45 deny tcp destination-port eq 593
 rule 50 deny udp destination-port eq 593
 rule 55 deny udp destination-port eq 1434
 rule 60 deny tcp destination-port eq 5554
 rule 65 deny tcp destination-port eq 135
 rule 70 deny udp destination-port eq netbios-ssn
 rule 75 deny tcp destination-port eq 139
 rule 80 deny udp destination-port eq 135
#
acl number 3022
 rule 10 permit ip vpn-instance mgt source ************** ************
 rule 15 permit ip vpn-instance mgt source ************ **************
 rule 20 permit ip vpn-instance mgt source ************ *************
 rule 25 permit ip vpn-instance mgt source ************ 0
 rule 30 permit ip vpn-instance mgt source ************ ************
 rule 35 permit ip vpn-instance mgt source ************ ************
 rule 40 permit ip vpn-instance mgt source ************ ************
 rule 45 permit ip vpn-instance mgt source ************* **********
#
security password
 #
 rule admin
  forbidden word changeme_123
#

 #
 authentication-scheme default0
 #
 authentication-scheme default1
 #
 authentication-scheme default
  authentication-mode local radius
 #
 authorization-scheme default
 #
 accounting-scheme default0
 #
 accounting-scheme default1
 #
 domain default0
 #
 domain default1
 #
 domain default_admin
#
license
#
interface Eth-Trunk0
 mtu 9600
 description To-[HAZZ-DC1-3F2-R04C17U03-ZGY-OSS-CE-RT02-HWNE40E]-Eth-Trunk0
 ipv6 enable
 ip address ***********3 ***************
 ipv6 address 2408:81B0:7000::5:1000:1000/127
 ospf network-type p2p
 ospf ldp-sync
 ospf timer ldp-sync hold-max-cost 60
 mode lacp-static
 lacp timeout fast
 mpls
 mpls ldp
 statistic enable
#
interface Eth-Trunk1
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk10_Bnet
 mode lacp-static
 lacp timeout fast
#
interface Eth-Trunk1.102
 vlan-type dot1q 102
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk10.102
 ip binding vpn-instance IMS_B
 ip address ************** ***************
 statistic enable
#
interface Eth-Trunk1.111
 vlan-type dot1q 111
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk10.111
 ip binding vpn-instance NKFVPN_B
 ip address *********** ***************
 statistic enable
#
interface Eth-Trunk1.601
 vlan-type dot1q 601
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk10.601
 ip binding vpn-instance MVSP_VS_B
 ipv6 enable
 ip address ************ ***************
 ipv6 address 2408:81B0:7000::5:1002:11/127
 statistic enable
#
interface Eth-Trunk1.606
 vlan-type dot1q 606
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk10.606
 ip binding vpn-instance MVSP_NMS_B
 ip address ************ ***************
 statistic enable
#
interface Eth-Trunk1.811
 vlan-type dot1q 811
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk10.811
 ip binding vpn-instance OSS_JT_B
 ip address ************* ***************
 statistic enable
#
interface Eth-Trunk1.817
 vlan-type dot1q 817
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk10.817
 ip binding vpn-instance IPRAN_JT_B
 ip address *************** ***************
 statistic enable
#
interface Eth-Trunk1.821
 vlan-type dot1q 821
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk10.821
 ip binding vpn-instance IOT_BSS_B
 ip address ************ ***************
 statistic enable
#
interface Eth-Trunk1.823
 vlan-type dot1q 823
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk10.823
 ip binding vpn-instance IoT_OSS_B
 ip address ************ ***************
 statistic enable
#
interface Eth-Trunk1.827
 vlan-type dot1q 827
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk10.827
 ip binding vpn-instance OTN_NMS_JT_B
 ip address ************ ***************
 statistic enable
#
interface Eth-Trunk1.837
 vlan-type dot1q 837
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk10.837
 ip binding vpn-instance 5GC_MGMN_B
 ipv6 enable
 ip address ************* ***************
 ipv6 address 2408:81B0:7000::5:1002:7/127
 statistic enable
#
interface Eth-Trunk1.839
 vlan-type dot1q 839
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk10.839
 ip binding vpn-instance 5G_MEC_B
 ip address ************ ***************
 statistic enable
#
interface Eth-Trunk1.851
 vlan-type dot1q 851
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk10.851
 ip binding vpn-instance HW_WDM_NMS_JT_B
 ip address ************* ***************
 statistic enable
#
interface Eth-Trunk1.867
 vlan-type dot1q 867
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk10.867
 ip binding vpn-instance IPRAN_NMS_JT_B
 ip address ************ ***************
 statistic enable
#
interface Eth-Trunk1.881
 vlan-type dot1q 881
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk10.881
 ip binding vpn-instance IOTPT_ZW_B
 ip address ************ ***************
 statistic enable
#
interface Eth-Trunk1.883
 vlan-type dot1q 883
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk10.883
 ip binding vpn-instance CN_MGMN_B
 ip address ************* ***************
 statistic enable
#
interface Eth-Trunk1.885
 vlan-type dot1q 885
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk10.885
 ip binding vpn-instance 5GC_NEF_B
 ipv6 enable
 ip address ************ ***************
 ipv6 address 2408:8140:C0FF:F900::1:3/127
 statistic enable
#
interface Eth-Trunk1.887
 vlan-type dot1q 887
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk10.887
 ip binding vpn-instance 5G_RCS_B
 ipv6 enable
 ip address ************ ***************
 ipv6 address 2408:81B0:7000::5:1002:B/127
 statistic enable
#
interface Eth-Trunk1.907
 vlan-type dot1q 907
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk10.907
 ip binding vpn-instance UTC_MGMN_B
 ipv6 enable
 ip address ************ ***************
 ipv6 address 2408:81B0:7000::5:1002:3/127
 statistic enable
#
interface Eth-Trunk1.917
 vlan-type dot1q 917
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk10.917
 ip binding vpn-instance SDOTN_JT_B
 ip address ************* ***************
 statistic enable
#
interface Eth-Trunk1.937
 vlan-type dot1q 937
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk10.937
 ip binding vpn-instance CU_OTNCPE_SDN_B
 ip address *********** ***************
 statistic enable
#
interface Eth-Trunk1.947
 vlan-type dot1q 947
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk10.947
 ip binding vpn-instance 5GC_Test_B
 ipv6 enable
 ipv6 address 2408:81B0:A00:1:F000::3/127
 statistic enable
#
interface Eth-Trunk1.951
 vlan-type dot1q 951
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk10.951
 ip binding vpn-instance 5GC_TANZHEN_B
 ip address ************* ***************
 statistic enable
#
interface Eth-Trunk1.981
 vlan-type dot1q 981
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk10.981
 ip binding vpn-instance SDOTN_TEMP_B
 ip address ************* ***************
 statistic enable
#
interface Eth-Trunk1.4010
 vlan-type dot1q 4010
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk10.4010
 ip binding vpn-instance SDOTN_AH_ZX_B
 ip address ************2 ***************
 statistic enable
#
interface Eth-Trunk1.4011
 vlan-type dot1q 4011
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk10.4011
 ip binding vpn-instance SDOTN_FJ_ZX_B
 ip address ************4 ***************
 statistic enable
#
interface Eth-Trunk1.4012
 vlan-type dot1q 4012
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk10.4012
 ip binding vpn-instance SDONT_CQ_ZX_B
 ip address ************ ***************
 statistic enable
#
interface Eth-Trunk1.4013
 vlan-type dot1q 4013
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk10.4013
 ip binding vpn-instance SDOTN_XZ_ZX_B
 ip address ************ ***************
 statistic enable
#
interface Eth-Trunk1.4014
 vlan-type dot1q 4014
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk10.4014
 ip binding vpn-instance SDOTN_HB_ZX_B
 ip address ************0 ***************
 statistic enable
#
interface Eth-Trunk1.4015
 vlan-type dot1q 4015
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk10.4015
 ip binding vpn-instance SDOTN_HEN_ZX_B
 ip address ************8 ***************
 statistic enable
#
interface Eth-Trunk1.4016
 vlan-type dot1q 4016
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk10.4016
 ip binding vpn-instance SDOTN_JX_ZX_B
 ip address ************* ***************
 statistic enable
#
interface Eth-Trunk1.4018
 vlan-type dot1q 4018
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk10.4018
 ip binding vpn-instance SDOTN_SX_ZX_B
 ip address ************* ***************
 statistic enable
#
interface Eth-Trunk1.4019
 vlan-type dot1q 4019
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk10.4019
 ip binding vpn-instance SDOTN_LN_ZX_B
 ip address ************* ***************
 statistic enable
#
interface Eth-Trunk1.4020
 vlan-type dot1q 4020
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk10.4020
 ip binding vpn-instance SDOTN_HN_ZX_B
 ip address ************* ***************
 statistic enable
#
interface Eth-Trunk1.4021
 vlan-type dot1q 4021
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk10.4021
 ip binding vpn-instance SDOTN_GX_ZX_B
 ip address ************* ***************
 statistic enable
#
interface Eth-Trunk1.4022
 vlan-type dot1q 4022
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk10.4022
 ip binding vpn-instance SDOTN_SC_ZX_B
 ip address ************* ***************
 statistic enable
#
interface Eth-Trunk1.4023
 vlan-type dot1q 4023
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk10.4023
 ip binding vpn-instance SDOTN_YN_ZX_B
 ip address ************* ***************
 statistic enable
#
interface Eth-Trunk1.4024
 vlan-type dot1q 4024
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk10.4024
 ip binding vpn-instance SDOTN_GS_HW_B
 ip address ************* ***************
 statistic enable
#
interface Eth-Trunk1.4025
 vlan-type dot1q 4025
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk10.4025
 ip binding vpn-instance SDOTN_JX_HW_B
 ip address ************* ***************
 statistic enable
#
interface Eth-Trunk1.4026
 vlan-type dot1q 4026
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk10.4026
 ip binding vpn-instance SDOTN_YN_HW_B
 ip address ************06 ***************
 statistic enable
#
interface Eth-Trunk1.4027
 vlan-type dot1q 4027
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk10.4027
 ip binding vpn-instance SDOTN_XZ_HW_B
 ip address ************14 ***************
 statistic enable
#
interface Eth-Trunk2
 description To-[ZYSJJD-1#-H3C-S12516F-AF-1]_RAGG1_2*10GE
 ip binding vpn-instance vrf_169
 ipv6 enable
 ip address ************* ***************
 ipv6 address 2408:8001:680:8000::BD3/127
 mode lacp-static
 port-mirroring inbound
 port-mirroring outbound
 port-mirroring to observe-index 1
#
interface Eth-Trunk9
 description To-[hazz-zyl-e-3]-Bundle-Ether9_Anet
 mode lacp-static
#
interface Eth-Trunk9.10
 vlan-type dot1q 10
 description To-[hazz-zyl-e-3]-Bundle-Ether9.10
 ip binding vpn-instance BSS-SN_A
 ip address ************** ***************
 statistic enable
#
interface Eth-Trunk9.20
 vlan-type dot1q 20
 description To-[hazz-zyl-e-3]-Bundle-Ether9.20
 ip binding vpn-instance OSS-SN_A
 ip address ************** ***************
 statistic enable
#
interface Eth-Trunk9.30
 vlan-type dot1q 30
 description To-[hazz-zyl-e-3]-Bundle-Ether9.30
 ip binding vpn-instance MSS-SN_A
 ip address ************** ***************
 statistic enable
#
interface Eth-Trunk9.90
 vlan-type dot1q 90
 description To-[hazz-zyl-e-3]-Bundle-Ether9.90
 ip binding vpn-instance NCRP_ZZRP_A
 ip address ************ ***************
 statistic enable
#
interface Eth-Trunk9.93
 vlan-type dot1q 93
 description To-[hazz-zyl-e-3]-Bundle-Ether9.93
 ip binding vpn-instance ha_dhtsgz_A
 ip address ************** ***************
 statistic enable
#
interface Eth-Trunk9.96
 vlan-type dot1q 96
 description To-[hazz-zyl-e-3]-Bundle-Ether9.96
 ip binding vpn-instance VRM-H_A
 ip address ************* ***************
 statistic enable
#
interface Eth-Trunk9.97
 vlan-type dot1q 97
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk9.97
 ip binding vpn-instance CDN_MGMN-H_A
 ip address ************* ***************
 statistic enable
#
interface Eth-Trunk9.98
 vlan-type dot1q 98
 description To-[hazz-zyl-e-3]-Bundle-Ether9.98
 ip binding vpn-instance TAC_VSS_A
 ip address ************** ***************
 statistic enable
#
interface Eth-Trunk9.99
 vlan-type dot1q 99
 description To-[hazz-zyl-e-3]-Bundle-Ether9.99
 ip binding vpn-instance GLW_BVMP-H_A
 ip address ************* ***************
 statistic enable
#
interface Eth-Trunk9.100
 vlan-type dot1q 100
 description To-[hazz-zyl-e-3]-Bundle-Ether9.100
 ip binding vpn-instance TAC_DATA_A
 ip address ************** ***************
 statistic enable
#
interface Eth-Trunk9.101
 vlan-type dot1q 101
 description To-[hazz-zyl-e-3]-Bundle-Ether9.101
 ip binding vpn-instance SMAN_NMS_JT_A
 ip address ************ ***************
 statistic enable
#
interface Eth-Trunk9.102
 vlan-type dot1q 102
 description To-[hazz-zyl-e-3]-Bundle-Ether9.102
 ip binding vpn-instance SMAN_NMS_CJ_A
 ip address ************ ***************
 statistic enable
#
interface Eth-Trunk9.103
 vlan-type dot1q 103
 description To-[hazz-zyl-e-3]-Bundle-Ether9.103
 ip binding vpn-instance DCI-CU_ZWZX-9_A
 ip address *********** ***************
 statistic enable
#
interface Eth-Trunk9.104
 vlan-type dot1q 104
 description To-[hazz-zyl-e-3]-Bundle-Ether9.104
 ip binding vpn-instance ZNZX-NMS_A
 ip address *********** ***************
 statistic enable
#
interface Eth-Trunk9.105
 vlan-type dot1q 105
 description To-[hazz-zyl-e-3]-Bundle-Ether9.105
 ip binding vpn-instance ZNZX_A
 ip address *********** ***************
 statistic enable
#
interface Eth-Trunk9.106
 vlan-type dot1q 106
 description To-[hazz-zyl-e-3]-Bundle-Ether9.106
 ip binding vpn-instance IDC_CONTROL-H_A
 ip address ************** ***************
 statistic enable
#
interface Eth-Trunk9.107
 vlan-type dot1q 107
 description To-[hazz-zyl-e-3]-Bundle-Ether9.107
 ip binding vpn-instance IPRAN_NMS_JT_A
 ip address ************** ***************
 statistic enable
#
interface Eth-Trunk9.110
 vlan-type dot1q 110
 description To-[hazz-zyl-e-3]-Bundle-Ether9.110
 ip binding vpn-instance BSS-EW_A
 ip address ************* ***************
 statistic enable
#
interface Eth-Trunk9.120
 vlan-type dot1q 120
 description To-[hazz-zyl-e-3]-Bundle-Ether9.120
 ip binding vpn-instance OSS-EW_A
 ip address ************* ***************
 statistic enable
#
interface Eth-Trunk9.130
 vlan-type dot1q 130
 description To-[hazz-zyl-e-3]-Bundle-Ether9.130
 ip binding vpn-instance MSS-EW_A
 ip address ************* ***************
 statistic enable
#
interface Eth-Trunk9.200
 vlan-type dot1q 200
 description To-[hazz-zyl-e-3]-Bundle-Ether9.200
 ip binding vpn-instance 5G-TF_A
 ip address ************** ***************
 statistic enable
#
interface Eth-Trunk36
 description To-ZYJD1B3F2-06P11L15U-OSS-HW9000E-1-Eth-Trunk36
 mode lacp-static
 lacp timeout fast
#
interface Eth-Trunk36.10
 vlan-type dot1q 10
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.10
 ip binding vpn-instance BSS-SN_A
 ipv6 enable
 ip address ************ ***************
 ipv6 address 2408:81B0:7000::5:1000:1002/127
 statistic enable
#
interface Eth-Trunk36.20
 vlan-type dot1q 20
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.20
 ip binding vpn-instance OSS-SN_A
 ipv6 enable
 ip address ************ ***************
 ipv6 address 2408:81B0:7000::5:1000:101A/127
 statistic enable
#
interface Eth-Trunk36.30
 vlan-type dot1q 30
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.30
 ip binding vpn-instance MSS-SN_A
 ipv6 enable
 ip address ************ ***************
 ipv6 address 2408:81B0:7000::5:1000:101E/127
 statistic enable
#
interface Eth-Trunk36.90
 vlan-type dot1q 90
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.90
 ip binding vpn-instance NCRP_ZZRP_A
 ipv6 enable
 ip address ************ ***************
 ipv6 address 2408:81B0:7000::5:1000:1020/127
 statistic enable
#
interface Eth-Trunk36.93
 vlan-type dot1q 93
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.93
 ip binding vpn-instance ha_dhtsgz_A
 ipv6 enable
 ip address ************ ***************
 ipv6 address 2408:81B0:7000::5:1000:1022/127
 statistic enable
#
interface Eth-Trunk36.96
 vlan-type dot1q 96
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.96
 ip binding vpn-instance VRM-H_A
 ipv6 enable
 ip address ************ ***************
 ipv6 address 2408:81B0:7000::5:1000:1024/127
 statistic enable
#
interface Eth-Trunk36.97
 vlan-type dot1q 97
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.97
 ip binding vpn-instance CDN_MGMN-H_A
 ip address ************ ***************
 statistic enable
#
interface Eth-Trunk36.98
 vlan-type dot1q 98
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.98
 ip binding vpn-instance TAC_VSS_A
 ipv6 enable
 ip address ************ ***************
 ipv6 address 2408:81B0:7000::5:1000:1026/127
 statistic enable
#
interface Eth-Trunk36.99
 vlan-type dot1q 99
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.99
 ip binding vpn-instance GLW_BVMP-H_A
 ip address ************* ***************
 statistic enable
#
interface Eth-Trunk36.100
 vlan-type dot1q 100
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.100
 ip binding vpn-instance TAC_DATA_A
 ipv6 enable
 ip address ************ ***************
 ipv6 address 2408:81B0:7000::5:1000:1004/127
 statistic enable
#
interface Eth-Trunk36.101
 vlan-type dot1q 101
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.101
 ip binding vpn-instance SMAN_NMS_JT_A
 ipv6 enable
 ip address *********** ***************
 ipv6 address 2408:81B0:7000::5:1000:1006/127
 statistic enable
#
interface Eth-Trunk36.102
 vlan-type dot1q 102
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.102
 ip binding vpn-instance SMAN_NMS_CJ_A
 ipv6 enable
 ip address *********** ***************
 ipv6 address 2408:81B0:7000::5:1000:1008/127
 statistic enable
#
interface Eth-Trunk36.103
 vlan-type dot1q 103
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.103
 ip binding vpn-instance DCI-CU_ZWZX-9_A
 ipv6 enable
 ip address *********** ***************
 ipv6 address 2408:81B0:7000::5:1000:100A/127
 statistic enable
#
interface Eth-Trunk36.104
 vlan-type dot1q 104
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.104
 ip binding vpn-instance ZNZX-NMS_A
 ipv6 enable
 ip address *********** ***************
 ipv6 address 2408:81B0:7000::5:1000:100C/127
 statistic enable
#
interface Eth-Trunk36.105
 vlan-type dot1q 105
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.105
 ip binding vpn-instance ZNZX_A
 ipv6 enable
 ip address *********** ***************
 ipv6 address 2408:81B0:7000::5:1000:100E/127
 statistic enable
#
interface Eth-Trunk36.106
 vlan-type dot1q 106
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.106
 ip binding vpn-instance IDC_CONTROL-H_A
 ipv6 enable
 ip address ***********3 ***************
 ipv6 address 2408:81B0:7000::5:1000:1010/127
 statistic enable
#
interface Eth-Trunk36.107
 vlan-type dot1q 107
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.107
 ip binding vpn-instance IPRAN_NMS_JT_A
 ipv6 enable
 ip address ***********7 ***************
 ipv6 address 2408:81B0:7000::5:1000:1012/127
 statistic enable
#
interface Eth-Trunk36.110
 vlan-type dot1q 110
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.110
 ip binding vpn-instance BSS-EW_A
 ipv6 enable
 ip address ***********1 ***************
 ipv6 address 2408:81B0:7000::5:1000:1014/127
 statistic enable
#
interface Eth-Trunk36.111
 vlan-type dot1q 111
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.111
 ip binding vpn-instance NKFVPN_B
 ipv6 enable
 ip address ***********41 ***************
 ipv6 address 2408:81B0:7000::5:1000:102A/127
 statistic enable
#
interface Eth-Trunk36.120
 vlan-type dot1q 120
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.120
 ip binding vpn-instance OSS-EW_A
 ipv6 enable
 ip address ***********5 ***************
 ipv6 address 2408:81B0:7000::5:1000:1016/127
 statistic enable
#
interface Eth-Trunk36.130
 vlan-type dot1q 130
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.130
 ip binding vpn-instance MSS-EW_A
 ipv6 enable
 ip address ***********9 ***************
 ipv6 address 2408:81B0:7000::5:1000:1018/127
 statistic enable
#
interface Eth-Trunk36.169
 vlan-type dot1q 169
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.169
 ip binding vpn-instance vrf_169
 ipv6 enable
 ip address ************* ***************
 ipv6 address 2408:81B0:7000::5:1000:1C/127
 statistic enable
#
interface Eth-Trunk36.200
 vlan-type dot1q 200
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.200
 ip binding vpn-instance 5G-TF_A
 ipv6 enable
 ip address ***********7 ***************
 ipv6 address 2408:81B0:7000::5:1000:101C/127
 statistic enable
#
interface Eth-Trunk36.500
 vlan-type dot1q 500
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.500
 ip binding vpn-instance vrf_406
 ipv6 enable
 ip address ************* ***************
 ipv6 address 2408:81B0:7000::5:1000:1076/127
 statistic enable
#
interface Eth-Trunk36.601
 vlan-type dot1q 601
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.601
 ip binding vpn-instance MVSP_VS_B
 ipv6 enable
 ip address ***********13 ***************
 ipv6 address 2408:81B0:7000::5:1000:104E/127
 statistic enable
#
interface Eth-Trunk36.606
 vlan-type dot1q 606
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.606
 ip binding vpn-instance MVSP_NMS_B
 ipv6 enable
 ip address ***********17 ***************
 ipv6 address 2408:81B0:7000::5:1000:1050/127
 statistic enable
#
interface Eth-Trunk36.811
 vlan-type dot1q 811
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.811
 ip binding vpn-instance OSS_JT_B
 ipv6 enable
 ip address ***********21 ***************
 ipv6 address 2408:81B0:7000::5:1000:1052/127
 statistic enable
#
interface Eth-Trunk36.817
 vlan-type dot1q 817
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.817
 ip binding vpn-instance IPRAN_JT_B
 ipv6 enable
 ip address ***********25 ***************
 ipv6 address 2408:81B0:7000::5:1000:1054/127
 statistic enable
#
interface Eth-Trunk36.821
 vlan-type dot1q 821
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.821
 ip binding vpn-instance IOT_BSS_B
 ipv6 enable
 ip address ***********29 ***************
 ipv6 address 2408:81B0:7000::5:1000:1056/127
 statistic enable
#
interface Eth-Trunk36.823
 vlan-type dot1q 823
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.823
 ip binding vpn-instance IoT_OSS_B
 ipv6 enable
 ip address ************ ***************
 ipv6 address 2408:81B0:7000::5:1000:1058/127
 statistic enable
#
interface Eth-Trunk36.827
 vlan-type dot1q 827
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.827
 ip binding vpn-instance OTN_NMS_JT_B
 ipv6 enable
 ip address ************ ***************
 ipv6 address 2408:81B0:7000::5:1000:105A/127
 statistic enable
#
interface Eth-Trunk36.837
 vlan-type dot1q 837
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.837
 ip binding vpn-instance 5GC_MGMN_B
 ipv6 enable
 ip address ************ ***************
 ipv6 address 2408:81B0:7000::5:1000:105C/127
 statistic enable
#
interface Eth-Trunk36.839
 vlan-type dot1q 839
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.839
 ip binding vpn-instance 5G_MEC_B
 ipv6 enable
 ip address ************ ***************
 ipv6 address 2408:81B0:7000::5:1000:105E/127
 statistic enable
#
interface Eth-Trunk36.851
 vlan-type dot1q 851
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.851
 ip binding vpn-instance HW_WDM_NMS_JT_B
 ip address ************* ***************
 statistic enable
#
interface Eth-Trunk36.867
 vlan-type dot1q 867
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.867
 ip binding vpn-instance IPRAN_NMS_JT_B
 ipv6 enable
 ip address *********** ***************
 ipv6 address 2408:81B0:7000::5:1000:1060/127
 statistic enable
#
interface Eth-Trunk36.881
 vlan-type dot1q 881
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.881
 ip binding vpn-instance IOTPT_ZW_B
 ipv6 enable
 ip address *********** ***************
 ipv6 address 2408:81B0:7000::5:1000:1062/127
 statistic enable
#
interface Eth-Trunk36.883
 vlan-type dot1q 883
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.883
 ip binding vpn-instance CN_MGMN_B
 ipv6 enable
 ip address *********** ***************
 ipv6 address 2408:81B0:7000::5:1000:1064/127
 statistic enable
#
interface Eth-Trunk36.885
 vlan-type dot1q 885
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.885
 ip binding vpn-instance 5GC_NEF_B
 ipv6 enable
 ip address *********** ***************
 ipv6 address 2408:81B0:7000::5:1000:1066/127
 statistic enable
#
interface Eth-Trunk36.887
 vlan-type dot1q 887
 description To-[ZYJD1B3F2-06P11L15U-OSS-HW9000E-1]-Eth-Trunk36.887
 ip binding vpn-instance 5G_RCS_B
 ipv6 enable
 ip address *********** ***************
 ipv6 address 2408:81B0:7000::5:1000:1068/127
 statistic enable
#
interface Eth-Trunk36.907
 vlan-type dot1q 907
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.907
 ip binding vpn-instance UTC_MGMN_B
 ipv6 enable
 ip address ***********3 ***************
 ipv6 address 2408:81B0:7000::5:1000:106A/127
 statistic enable
#
interface Eth-Trunk36.917
 vlan-type dot1q 917
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.917
 ip binding vpn-instance SDOTN_JT_B
 ipv6 enable
 ip address ***********7 ***************
 ipv6 address 2408:81B0:7000::5:1000:106C/127
 statistic enable
#
interface Eth-Trunk36.937
 vlan-type dot1q 937
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.937
 ip binding vpn-instance CU_OTNCPE_SDN_B
 ipv6 enable
 ip address ***********1 ***************
 ipv6 address 2408:81B0:7000::5:1000:106E/127
 statistic enable
#
interface Eth-Trunk36.947
 vlan-type dot1q 947
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.947
 ip binding vpn-instance 5GC_Test_B
 ipv6 enable
 ip address ***********5 ***************
 ipv6 address 2408:81B0:7000::5:1000:1070/127
 statistic enable
#
interface Eth-Trunk36.951
 vlan-type dot1q 951
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.951
 ip binding vpn-instance 5GC_TANZHEN_B
 ipv6 enable
 ip address ***********9 ***************
 ipv6 address 2408:81B0:7000::5:1000:1072/127
 statistic enable
#
interface Eth-Trunk36.981
 vlan-type dot1q 981
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.981
 ip binding vpn-instance SDOTN_TEMP_B
 ipv6 enable
 ip address ***********3 ***************
 ipv6 address 2408:81B0:7000::5:1000:1074/127
 statistic enable
#
interface Eth-Trunk36.1020
 vlan-type dot1q 1020
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.1020
 ip binding vpn-instance IMS_B
 ipv6 enable
 ip address ***********37 ***************
 ipv6 address 2408:81B0:7000::5:1000:1028/127
 statistic enable
#
interface Eth-Trunk36.4010
 vlan-type dot1q 4010
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.4010
 ip binding vpn-instance SDOTN_AH_ZX_B
 ipv6 enable
 ip address ***********45 ***************
 ipv6 address 2408:81B0:7000::5:1000:102C/127
 statistic enable
#
interface Eth-Trunk36.4011
 vlan-type dot1q 4011
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.4011
 ip binding vpn-instance SDOTN_FJ_ZX_B
 ipv6 enable
 ip address ***********49 ***************
 ipv6 address 2408:81B0:7000::5:1000:102E/127
 statistic enable
#
interface Eth-Trunk36.4012
 vlan-type dot1q 4012
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.4012
 ip binding vpn-instance SDONT_CQ_ZX_B
 ipv6 enable
 ip address ************3 ***************
 ipv6 address 2408:81B0:7000::5:1000:1030/127
 statistic enable
#
interface Eth-Trunk36.4013
 vlan-type dot1q 4013
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.4013
 ip binding vpn-instance SDOTN_XZ_ZX_B
 ipv6 enable
 ip address ************7 ***************
 ipv6 address 2408:81B0:7000::5:1000:1032/127
 statistic enable
#
interface Eth-Trunk36.4014
 vlan-type dot1q 4014
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.4014
 ip binding vpn-instance SDOTN_HB_ZX_B
 ipv6 enable
 ip address ************* ***************
 ipv6 address 2408:81B0:7000::5:1000:1034/127
 statistic enable
#
interface Eth-Trunk36.4015
 vlan-type dot1q 4015
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.4015
 ip binding vpn-instance SDOTN_HEN_ZX_B
 ipv6 enable
 ip address ************* ***************
 ipv6 address 2408:81B0:7000::5:1000:1036/127
 statistic enable
#
interface Eth-Trunk36.4016
 vlan-type dot1q 4016
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.4016
 ip binding vpn-instance SDOTN_JX_ZX_B
 ipv6 enable
 ip address ************* ***************
 ipv6 address 2408:81B0:7000::5:1000:1038/127
 statistic enable
#
interface Eth-Trunk36.4018
 vlan-type dot1q 4018
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.4018
 ip binding vpn-instance SDOTN_SX_ZX_B
 ipv6 enable
 ip address ************* ***************
 ipv6 address 2408:81B0:7000::5:1000:103A/127
 statistic enable
#
interface Eth-Trunk36.4019
 vlan-type dot1q 4019
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.4019
 ip binding vpn-instance SDOTN_LN_ZX_B
 ipv6 enable
 ip address ***********77 ***************
 ipv6 address 2408:81B0:7000::5:1000:103C/127
 statistic enable
#
interface Eth-Trunk36.4020
 vlan-type dot1q 4020
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.4020
 ip binding vpn-instance SDOTN_HN_ZX_B
 ipv6 enable
 ip address ************* ***************
 ipv6 address 2408:81B0:7000::5:1000:103E/127
 statistic enable
#
interface Eth-Trunk36.4021
 vlan-type dot1q 4021
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.4021
 ip binding vpn-instance SDOTN_GX_ZX_B
 ipv6 enable
 ip address ************* ***************
 ipv6 address 2408:81B0:7000::5:1000:1040/127
 statistic enable
#
interface Eth-Trunk36.4022
 vlan-type dot1q 4022
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.4022
 ip binding vpn-instance SDOTN_SC_ZX_B
 ipv6 enable
 ip address ************* ***************
 ipv6 address 2408:81B0:7000::5:1000:1042/127
 statistic enable
#
interface Eth-Trunk36.4023
 vlan-type dot1q 4023
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.4023
 ip binding vpn-instance SDOTN_YN_ZX_B
 ipv6 enable
 ip address ***********93 ***************
 ipv6 address 2408:81B0:7000::5:1000:1044/127
 statistic enable
#
interface Eth-Trunk36.4024
 vlan-type dot1q 4024
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.4024
 ip binding vpn-instance SDOTN_GS_HW_B
 ipv6 enable
 ip address ***********97 ***************
 ipv6 address 2408:81B0:7000::5:1000:1046/127
 statistic enable
#
interface Eth-Trunk36.4025
 vlan-type dot1q 4025
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.4025
 ip binding vpn-instance SDOTN_JX_HW_B
 ipv6 enable
 ip address ***********01 ***************
 ipv6 address 2408:81B0:7000::5:1000:1048/127
 statistic enable
#
interface Eth-Trunk36.4026
 vlan-type dot1q 4026
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.4026
 ip binding vpn-instance SDOTN_YN_HW_B
 ipv6 enable
 ip address ***********05 ***************
 ipv6 address 2408:81B0:7000::5:1000:104A/127
 statistic enable
#
interface Eth-Trunk36.4027
 vlan-type dot1q 4027
 description To-[ZYJD1B3F2-06P11L03U-OSS-HW9000E-1]-Eth-Trunk36.4027
 ip binding vpn-instance SDOTN_XZ_HW_B
 ipv6 enable
 ip address ***********09 ***************
 ipv6 address 2408:81B0:7000::5:1000:104C/127
 statistic enable
#
interface Virtual-Template0
 ppp authentication-mode auto
#
interface GigabitEthernet0/0/0
 speed auto
 duplex auto
 description TO-[ZY1B3F1-08P15L41U-Pub-HW5320-DWSW01]-GE0/0/35+GE0/0/36_beiyong
 undo shutdown
 ip binding vpn-instance mgt
 ip address ************ **************
#
interface GigabitEthernet1/0/0
 shutdown
 ipv6 enable
 undo dcn
 statistic enable
#
interface GigabitEthernet1/0/1
 description To-ZY1B4F406_Rne
 undo shutdown
 undo dcn
 statistic enable
#
interface GigabitEthernet1/0/1.500
 vlan-type dot1q 500
 description To-[ZY1B4F406_Rne]
 ip binding vpn-instance vrf_406
 ip address ************** ***************
 statistic enable
#
interface GigabitEthernet1/0/2
 description To_S12516_1_XGE4/0/3
 undo shutdown
 eth-trunk 2
 undo dcn
#
interface GigabitEthernet1/0/3
 shutdown
 undo dcn
#
interface GigabitEthernet1/0/4
 shutdown
 undo dcn
#
interface GigabitEthernet1/0/5
 shutdown
 undo dcn
#
interface GigabitEthernet1/0/6
 shutdown
 undo dcn
#
interface GigabitEthernet1/0/7
 shutdown
 undo dcn
#
interface GigabitEthernet1/0/8
 shutdown
 undo dcn
#
interface GigabitEthernet1/0/9
 shutdown
 undo dcn
#
interface GigabitEthernet1/0/10
 shutdown
 undo dcn
#
interface GigabitEthernet1/0/11
 shutdown
 undo dcn
#
interface GigabitEthernet1/0/12
 shutdown
 undo dcn
#
interface GigabitEthernet1/0/13
 shutdown
 undo dcn
#
interface GigabitEthernet1/0/14
 shutdown
 undo dcn
#
interface GigabitEthernet1/0/15
 shutdown
 undo dcn
#
interface GigabitEthernet1/0/16
 shutdown
 undo dcn
#
interface GigabitEthernet1/0/17
 shutdown
 undo dcn
#
interface GigabitEthernet1/0/18
 shutdown
 undo dcn
#
interface GigabitEthernet1/0/19
 shutdown
 undo dcn
#
interface GigabitEthernet2/0/0
 description To-[ZYSJJD-1#-H3C-S12516F-AF-1]_TGE4/0/6_1*10GE
 undo shutdown
 eth-trunk 2
 undo dcn
#
interface GigabitEthernet2/0/1
 shutdown
 undo dcn
#
interface GigabitEthernet2/0/2
 shutdown
 undo dcn
#
interface GigabitEthernet2/0/3
 shutdown
 undo dcn
#
interface GigabitEthernet2/0/4
 shutdown
 undo dcn
#
interface GigabitEthernet2/0/5
 shutdown
 undo dcn
#
interface GigabitEthernet2/0/6
 shutdown
 undo dcn
#
interface GigabitEthernet2/0/7
 shutdown
 undo dcn
#
interface GigabitEthernet2/0/8
 shutdown
 undo dcn
#
interface GigabitEthernet2/0/9
 shutdown
 undo dcn
#
interface GigabitEthernet2/0/10
 shutdown
 undo dcn
#
interface GigabitEthernet2/0/11
 shutdown
 undo dcn
#
interface GigabitEthernet2/0/12
 shutdown
 undo dcn
#
interface GigabitEthernet2/0/13
 shutdown
 undo dcn
#
interface GigabitEthernet2/0/14
 shutdown
 undo dcn
#
interface GigabitEthernet2/0/15
 shutdown
 undo dcn
#
interface GigabitEthernet2/0/16
 shutdown
 undo dcn
#
interface GigabitEthernet2/0/17
 shutdown
 undo dcn
#
interface GigabitEthernet2/0/18
 shutdown
 undo dcn
#
interface GigabitEthernet2/0/19
 shutdown
 undo dcn
#
interface 100GE1/1/0
 description To-[HAZZ-DC1-3F2-R04C17U03-ZGY-OSS-CE-RT02-HWNE40E]-100GE1/1/0
 undo shutdown
 eth-trunk 0
 undo dcn
#
interface 100GE1/1/1
 description To-[HAZZ-DC1-3F2-R04C17U03-ZGY-OSS-CE-RT02-HWNE40E]-100GE1/1/1
 undo shutdown
 eth-trunk 0
 undo dcn
#
interface 100GE2/1/0
 description To-[HAZZ-DC1-3F2-R04C17U03-ZGY-OSS-CE-RT02-HWNE40E]-100GE2/1/0
 undo shutdown
 eth-trunk 0
 undo dcn
#
interface 100GE2/1/1
 description To-[HAZZ-DC1-3F2-R04C17U03-ZGY-OSS-CE-RT02-HWNE40E]-100GE2/1/1
 undo shutdown
 eth-trunk 0
 undo dcn
#
interface 100GE3/0/0
 carrier up-hold-time 3000
 carrier down-hold-time 150
 description To-[HAZZ-ZYL-N40E-E1]-100GE12/0/0_[W-640N9043IPB]
 undo shutdown
 eth-trunk 1
 undo dcn
#
interface 100GE3/0/1
 carrier up-hold-time 3000
 carrier down-hold-time 150
 description To-[HAZZ-ZYL-N40E-E1]-100GE16/0/0_[W-640N9044IPB]
 undo shutdown
 eth-trunk 1
 undo dcn
#
interface 100GE3/1/0
 description To-[HAZZ-DC1-3F2-R04C17U03-ZGY-OSS-CE-RT02-HWNE40E]-100GE3/1/0
 undo shutdown
 eth-trunk 0
 undo dcn
#
interface 100GE3/1/1
 description To-[HAZZ-DC1-3F2-R04C17U03-ZGY-OSS-CE-RT02-HWNE40E]-100GE3/1/1
 undo shutdown
 eth-trunk 0
 undo dcn
#
interface 100GE4/0/0
 carrier up-hold-time 3000
 carrier down-hold-time 150
 description To-[HAZZ-ZYL-N40E-E1]-100GE11/0/0_[640N2133CTO-B]
 undo shutdown
 eth-trunk 1
 undo dcn
#
interface 100GE4/0/1
 carrier up-hold-time 3000
 carrier down-hold-time 150
 description To-[HAZZ-ZYL-N40E-E1]-100GE7/0/0_[W-640N2188CTO-B]
 undo shutdown
 eth-trunk 1
 undo dcn
#
interface 100GE4/1/0
 carrier up-hold-time 3000
 carrier down-hold-time 150
 description To-[hazz-zyl-e-3]-ASR9010-100GE0/3/0/1_[W-640N2106SJJDCTO-A]
 undo shutdown
 eth-trunk 9
 undo dcn
#
interface 100GE4/1/1
 description To-[ZY1B3F1-07P15L44U-HuiJuFenLiuQi-1]-100GE-1
 undo shutdown
 undo dcn
 port-observing observe-index 1
#
interface 100GE5/0/0
 description To-[ZYJD1B3F2-06P11L15U-OSS-HW9000E-1]-100GE2/0/5
 undo shutdown
 eth-trunk 36
 undo dcn
#
interface 100GE5/0/1
 description To-[ZYJD1B3F2-06P11L15U-OSS-HW9000E-1]-100GE2/0/6
 undo shutdown
 eth-trunk 36
 undo dcn
#
interface 100GE5/1/0
 carrier up-hold-time 3000
 carrier down-hold-time 150
 description To-[hazz-zyl-e-3]-ASR9010-100GE0/5/0/1-[W-640N2108SJJDCTO-A]
 undo shutdown
 eth-trunk 9
 undo dcn
#
interface 100GE5/1/1
 carrier up-hold-time 3000
 carrier down-hold-time 150
 description To-[HAZZ-ZYL-N40E-E1]-100GE4/1/0_[W-640N2273OTC-B]
 shutdown
 undo dcn
#
interface 100GE6/0/0
 description To-[ZYJD1B3F2-06P11L15U-OSS-HW9000E-1]-100GE2/0/7
 undo shutdown
 eth-trunk 36
 undo dcn
#
interface 100GE6/0/1
 description To-[ZYJD1B3F2-06P11L15U-OSS-HW9000E-1]-100GE2/0/8
 undo shutdown
 eth-trunk 36
 undo dcn
#
interface 100GE6/1/0
 shutdown
 undo dcn
#
interface 100GE6/1/1
 carrier up-hold-time 3000
 carrier down-hold-time 150
 description To-[HAZZ-ZYL-N40E-E1]-100GE13/1/0_[W-640N2274OTC-B]
 shutdown
 undo dcn
#
interface 100GE7/0/0
 description To-[ZYJD1B3F2-06P11L15U-OSS-HW9000E-1]-100GE3/0/5
 undo shutdown
 eth-trunk 36
 undo dcn
#
interface 100GE7/0/1
 description To-[ZYJD1B3F2-06P11L15U-OSS-HW9000E-1]-100GE3/0/6
 undo shutdown
 eth-trunk 36
 undo dcn
#
interface 100GE7/1/0
 carrier up-hold-time 3000
 carrier down-hold-time 150
 description To-[HAZZ-ZYL-N40E-E1]-HundredGigE0/5/1/0_[W-640N2251CTO-A]
 undo shutdown
 eth-trunk 9
 undo dcn
#
interface 100GE7/1/1
 shutdown
 undo dcn
#
interface 100GE8/0/0
 description To-[ZYJD1B3F2-06P11L15U-OSS-HW9000E-1]-100GE3/0/7
 undo shutdown
 eth-trunk 36
 undo dcn
#
interface 100GE8/0/1
 description To-[ZYJD1B3F2-06P11L15U-OSS-HW9000E-1]-100GE3/0/8
 undo shutdown
 eth-trunk 36
 undo dcn
#
interface 100GE8/1/0
 shutdown
 undo dcn
#
interface 100GE8/1/1
 shutdown
 undo dcn
#
interface 100GE9/0/0
 shutdown
 undo dcn
#
interface 100GE9/0/1
 shutdown
 undo dcn
#
interface 100GE9/1/0
 shutdown
 undo dcn
#
interface 100GE9/1/1
 shutdown
 undo dcn
#
interface HP-GE17/3/0
 shutdown
#
interface HP-GE17/3/1
 shutdown
#
interface HP-GE18/3/0
 shutdown
#
interface HP-GE18/3/1
 shutdown
#
interface LoopBack0
 ip address ************* ***************
#
interface NULL0
#
bfd bfd_trunk0:1 bind peer-ip default-ip interface 100GE1/1/0
 discriminator local 1101
 discriminator remote 101
 min-tx-interval 50
 min-rx-interval 50
 process-interface-status
#
bfd bfd_trunk0:2 bind peer-ip default-ip interface 100GE1/1/1
 discriminator local 1102
 discriminator remote 102
 min-tx-interval 50
 min-rx-interval 50
 process-interface-status
#
bfd bfd_trunk0:3 bind peer-ip default-ip interface 100GE2/1/0
 discriminator local 1103
 discriminator remote 103
 min-tx-interval 50
 min-rx-interval 50
 process-interface-status
#
bfd bfd_trunk0:4 bind peer-ip default-ip interface 100GE2/1/1
 discriminator local 1104
 discriminator remote 104
 min-tx-interval 50
 min-rx-interval 50
 process-interface-status
#
bfd bfd_trunk0:5 bind peer-ip default-ip interface 100GE3/1/0
 discriminator local 1105
 discriminator remote 105
 min-tx-interval 50
 min-rx-interval 50
 process-interface-status
#
bfd bfd_trunk0:6 bind peer-ip default-ip interface 100GE3/1/1
 discriminator local 1106
 discriminator remote 106
 min-tx-interval 50
 min-rx-interval 50
 process-interface-status
#
bfd bfd_trunk1:1 bind peer-ip default-ip interface 100GE3/0/0
 discriminator local 7104
 discriminator remote 7004
 min-tx-interval 100
 min-rx-interval 100
 process-interface-status
#
bfd bfd_trunk1:2 bind peer-ip default-ip interface 100GE3/0/1
 discriminator local 7105
 discriminator remote 7005
 min-tx-interval 100
 min-rx-interval 100
 process-interface-status
#
bfd bfd_trunk1:3 bind peer-ip default-ip interface 100GE4/0/0
 discriminator local 7106
 discriminator remote 7006
 min-tx-interval 100
 min-rx-interval 100
 process-interface-status
#
bfd bfd_trunk1:4 bind peer-ip default-ip interface 100GE4/0/1
 discriminator local 7107
 discriminator remote 7007
 min-tx-interval 100
 min-rx-interval 100
 process-interface-status
#
bgp 65159
 router-id *************
 undo default ipv4-unicast
 private-4-byte-as enable
 peer ************* as-number 65159
 peer ************* description To ZYJD1B3F2-04P17L03U-CK-NE40E-X16A-2
 peer ************* connect-interface LoopBack0
 peer ************* bfd min-tx-interval 100 min-rx-interval 100
 peer ************* bfd enable
 #
 ipv4-family unicast
  undo synchronization
  undo peer ************* enable
 #
 ipv4-family vpnv4
  policy vpn-target
  peer ************* enable
  peer ************* reflect-client
  peer ************* next-hop-local
 #
 ipv4-family vpn-instance 5G-TF_A
  peer ***********8 as-number 65159
  peer ***********8 description To_DianLianGJGX-5G-TN
  peer ***********8 bfd min-tx-interval 100 min-rx-interval 100
  peer ***********8 bfd enable
  peer ***********8 next-hop-local
  peer ************** as-number 9929
  peer ************** description To_DianLianGJGX-5G-TN
  peer ************** bfd min-tx-interval 100 min-rx-interval 100
  peer ************** bfd enable
  peer ************** ip-prefix DianLianGJGX import
  peer ************** ip-prefix DianLianGJGX export
 #
 ipv4-family vpn-instance 5GC_MGMN_B
  peer ************* as-number 38351
  peer ************* description To-VPN-5GC-MGMN
  peer ************* bfd min-tx-interval 100 min-rx-interval 100
  peer ************* bfd enable
  peer ************* route-policy rp_5GC_MGMN_out export
  peer ************ as-number 65159
  peer ************ description To-VPN-5GC-MGMN
  peer ************ bfd min-tx-interval 100 min-rx-interval 100
  peer ************ bfd enable
  peer ************ next-hop-local
 #
 ipv4-family vpn-instance 5GC_NEF_B
  peer *********** as-number 65159
  peer *********** description TO-5GC_NEF
  peer *********** bfd min-tx-interval 100 min-rx-interval 100
  peer *********** bfd enable
  peer *********** next-hop-local
  peer ************ as-number 38351
  peer ************ description TO-5GC_NEF
  peer ************ bfd min-tx-interval 100 min-rx-interval 100
  peer ************ bfd enable
  peer ************ ip-prefix 5gc_nef import
  peer ************ ip-prefix 5gc_nef export
 #
 ipv4-family vpn-instance 5GC_TANZHEN_B
  peer ************ as-number 65159
  peer ************ description description 5GC_TANZHEN
  peer ************ bfd min-tx-interval 100 min-rx-interval 100
  peer ************ bfd enable
  peer ************ next-hop-local
  peer ************* as-number 38351
  peer ************* description description 5GC_TANZHEN
  peer ************* bfd min-tx-interval 150 min-rx-interval 150
  peer ************* bfd enable
  peer ************* ip-prefix 5gc_tanzhen_in import
  peer ************* ip-prefix 5gc_tanzhen_out export
 #
 ipv4-family vpn-instance 5G_MEC_B
  peer ************ as-number 38351
  peer ************ description 5G_MEC
  peer ************ bfd min-tx-interval 100 min-rx-interval 100
  peer ************ bfd enable
  peer ************ route-policy rp_5G_MEC_out export
  peer ***********0 as-number 65159
  peer ***********0 description 5G_MEC
  peer ***********0 bfd min-tx-interval 100 min-rx-interval 100
  peer ***********0 bfd enable
  peer ***********0 next-hop-local
 #
 ipv4-family vpn-instance 5G_RCS_B
  peer ***********0 as-number 65159
  peer ***********0 description description 5G_RCS
  peer ***********0 bfd min-tx-interval 100 min-rx-interval 100
  peer ***********0 bfd enable
  peer ***********0 next-hop-local
  peer ************ as-number 38351
  peer ************ description description 5G_RCS
  peer ************ bfd min-tx-interval 100 min-rx-interval 100
  peer ************ bfd enable
  peer ************ route-policy rp_5G_RCS_out export
 #
 ipv4-family vpn-instance BSS-EW_A
  peer ***********2 as-number 65159
  peer ***********2 description TO-BSS-EW-1
  peer ***********2 bfd min-tx-interval 100 min-rx-interval 100
  peer ***********2 bfd enable
  peer ***********2 next-hop-local
  peer ************* as-number 9929
  peer ************* description TO-BSS-EW-1
  peer ************* bfd min-tx-interval 500 min-rx-interval 500
  peer ************* bfd enable
  peer ************* route-policy anet_ew_in import
  peer ************* route-policy anet_ew_out export
 #
 ipv4-family vpn-instance BSS-SN_A
  network ************* ***************
  network ************ ***************
  network ************ ***************
  network ************ ***************
  network ************* ***************
  network ************* ***************
  network ************** ***************
  peer ***********4 as-number 65159
  peer ***********4 description TO-BSS-1
  peer ***********4 bfd min-tx-interval 100 min-rx-interval 100
  peer ***********4 bfd enable
  peer ***********4 next-hop-local
  peer ************** as-number 9929
  peer ************** description TO-BSS-1
  peer ************** bfd min-tx-interval 100 min-rx-interval 100
  peer ************** bfd enable
  peer ************** route-policy anet import
  peer ************** route-policy to_BSS-SN_A export
 #
 ipv4-family vpn-instance CDN_MGMN-H_A
  peer 31.210.80.22 as-number 65159
  peer 31.210.80.22 description peer_to_FW_vfw_Bnet
  peer 31.210.80.22 bfd min-tx-interval 150 min-rx-interval 150
  peer 31.210.80.22 bfd enable
  peer 31.210.80.22 next-hop-local
  peer 130.40.255.69 as-number 9929
  peer 130.40.255.69 description peer_to_ER_CDN_MGMN-H_A
  peer 130.40.255.69 bfd min-tx-interval 150 min-rx-interval 150
  peer 130.40.255.69 bfd enable
  peer 130.40.255.69 ip-prefix pl_CDN_MGMN-H_A import
 #
 ipv4-family vpn-instance CN_MGMN_B
  peer *********** as-number 65159
  peer *********** description To_VPN_CN_MGMN
  peer *********** bfd min-tx-interval 100 min-rx-interval 100
  peer *********** bfd enable
  peer *********** next-hop-local
  peer 130.40.255.45 as-number 38351
  peer 130.40.255.45 description To_VPN_CN_MGMN
  peer 130.40.255.45 bfd min-tx-interval 150 min-rx-interval 150
  peer 130.40.255.45 bfd enable
  peer 130.40.255.45 route-policy rp_CN_MGMN_in import
  peer 130.40.255.45 route-policy rp_CN_MGMN_out export
 #
 ipv4-family vpn-instance CU_OTNCPE_SDN_B
  peer 38.80.64.23 as-number 38351
  peer 38.80.64.23 description To-CU_OTNCPE_SDN
  peer 38.80.64.23 bfd min-tx-interval 100 min-rx-interval 100
  peer 38.80.64.23 bfd enable
  peer 38.80.64.23 ip-prefix cu_otncpe_sdn import
  peer 38.80.64.23 route-policy rp_CU_OTNCPE_SDN_out export
  peer ***********2 as-number 65159
  peer ***********2 description To-CU_OTNCPE_SDN
  peer ***********2 bfd min-tx-interval 100 min-rx-interval 100
  peer ***********2 bfd enable
  peer ***********2 next-hop-local
 #
 ipv4-family vpn-instance DCI-CU_ZWZX-9_A
  peer *********** as-number 65159
  peer *********** description TO-DDCI-CU_ZWZX-9
  peer *********** bfd min-tx-interval 100 min-rx-interval 100
  peer *********** bfd enable
  peer *********** next-hop-local
  peer 58.88.38.42 as-number 9929
  peer 58.88.38.42 description TO-DDCI-CU_ZWZX-9
  peer 58.88.38.42 ip-prefix dc_bound import
  peer 58.88.38.42 ip-prefix dc_bound_out export
 #
 ipv4-family vpn-instance GLW_BVMP-H_A
  peer ***********18 as-number 65159
  peer ***********18 description GLW_BVMP-H
  peer ***********18 bfd min-tx-interval 150 min-rx-interval 150
  peer ***********18 bfd enable
  peer ***********18 next-hop-local
  peer 13*********** as-number 9929
  peer 13*********** description GLW_BVMP-H
  peer 13*********** bfd min-tx-interval 150 min-rx-interval 150
  peer 13*********** bfd enable
  peer 13*********** ip-prefix GLW_BVMP-H_in import
 #
 ipv4-family vpn-instance HW_WDM_NMS_JT_B
  peer ************* as-number 65159
  peer ************* description HW_WDM_NMS_JT_B
  peer ************* bfd min-tx-interval 150 min-rx-interval 150
  peer ************* bfd enable
  peer ************* next-hop-local
  peer ************* as-number 38351
  peer ************* description HW_WDM_NMS_JT
  peer ************* bfd min-tx-interval 150 min-rx-interval 150
  peer ************* bfd enable
  peer ************* ip-prefix pl_HW_WDM_NMS_JT_B_in import
 #
 ipv4-family vpn-instance IDC_CONTROL-H_A
  peer ***********4 as-number 65159
  peer ***********4 description IDC_CONTROL_H
  peer ***********4 bfd min-tx-interval 100 min-rx-interval 100
  peer ***********4 bfd enable
  peer ***********4 next-hop-local
  peer ************** as-number 9929
  peer ************** description IDC_CONTROL_H
  peer ************** bfd min-tx-interval 100 min-rx-interval 100
  peer ************** bfd enable
  peer ************** ip-prefix IDC_CONTROL_H import
  peer ************** ip-prefix IDC_CONTROL_H export
 #
 ipv4-family vpn-instance IMS_B
  peer ************** as-number 38351
  peer ************** description VPN-IMS
  peer ************** bfd min-tx-interval 100 min-rx-interval 100
  peer ************** bfd enable
  peer ************** route-policy IMS_VPN_out export
  peer ***********38 as-number 65159
  peer ***********38 description VPN-IMS
  peer ***********38 bfd min-tx-interval 100 min-rx-interval 100
  peer ***********38 bfd enable
  peer ***********38 next-hop-local
 #
 ipv4-family vpn-instance IOTPT_ZW_B
  peer *********** as-number 65159
  peer *********** description IOTPT_ZW
  peer *********** bfd min-tx-interval 100 min-rx-interval 100
  peer *********** bfd enable
  peer *********** next-hop-local
  peer ************ as-number 38351
  peer ************ description IOTPT_ZW
  peer ************ bfd min-tx-interval 150 min-rx-interval 150
  peer ************ bfd enable
  peer ************ ip-prefix iotpt_zw import
  peer ************ route-policy rp_IOTPT_ZW_out export
 #
 ipv4-family vpn-instance IOT_BSS_B
  peer ***********30 as-number 65159
  peer ***********30 description IOT_BSS
  peer ***********30 bfd min-tx-interval 100 min-rx-interval 100
  peer ***********30 bfd enable
  peer ***********30 next-hop-local
  peer ************ as-number 38351
  peer ************ description IOT_BSS
  peer ************ bfd min-tx-interval 100 min-rx-interval 100
  peer ************ bfd enable
  peer ************ ip-prefix IOT_BSS import
  peer ************ route-policy rp_IOT_BSS_out export
 #
 ipv4-family vpn-instance IPRAN_JT_B
  peer ***********26 as-number 65159
  peer ***********26 description VPN-IPRAN-JT
  peer ***********26 bfd min-tx-interval 100 min-rx-interval 100
  peer ***********26 bfd enable
  peer ***********26 next-hop-local
  peer *************** as-number 38351
  peer *************** description VPN-IPRAN-JT
  peer *************** bfd min-tx-interval 100 min-rx-interval 100
  peer *************** bfd enable
  peer *************** route-policy rp_IPRAN_JT_out export
 #
 ipv4-family vpn-instance IPRAN_NMS_JT_A
  peer ***********8 as-number 65159
  peer ***********8 description To-VPN-IPRAN_Anet
  peer ***********8 bfd min-tx-interval 100 min-rx-interval 100
  peer ***********8 bfd enable
  peer ***********8 next-hop-local
  peer *************5 as-number 9929
  peer *************5 description To-VPN-IPRAN_Anet
  peer *************5 bfd min-tx-interval 100 min-rx-interval 100
  peer *************5 bfd enable
  peer *************5 ip-prefix ipran_Anet import
  peer *************5 ip-prefix ipran_Anet export
 #
 ipv4-family vpn-instance IPRAN_NMS_JT_B
  peer *********** as-number 65159
  peer *********** description to_IPRAN_NMS_JT
  peer *********** bfd min-tx-interval 100 min-rx-interval 100
  peer *********** bfd enable
  peer *********** next-hop-local
  peer ************ as-number 38351
  peer ************ description to_IPRAN_NMS_JT
  peer ************ bfd min-tx-interval 100 min-rx-interval 100
  peer ************ bfd enable
  peer ************ route-policy rp_IPRAN_NMS_JT_out export
 #
 ipv4-family vpn-instance IoT_OSS_B
  network ************ ***************
  peer ***********2 as-number 65159
  peer ***********2 description to-IoT-OSS VPN
  peer ***********2 bfd min-tx-interval 100 min-rx-interval 100
  peer ***********2 bfd enable
  peer ***********2 next-hop-local
  peer ************ as-number 38351
  peer ************ description to-IoT-OSS VPN
  peer ************ bfd min-tx-interval 100 min-rx-interval 100
  peer ************ bfd enable
  peer ************ route-policy rp_IoT_OSS_out export
 #
 ipv4-family vpn-instance MSS-EW_A
  peer ***********0 as-number 65159
  peer ***********0 description TO-MSS-EW-1
  peer ***********0 bfd min-tx-interval 100 min-rx-interval 100
  peer ***********0 bfd enable
  peer ***********0 next-hop-local
  peer 130.40.255.29 as-number 9929
  peer 130.40.255.29 description TO-MSS-EW-1
  peer 130.40.255.29 bfd min-tx-interval 500 min-rx-interval 500
  peer 130.40.255.29 bfd enable
  peer 130.40.255.29 route-policy anet_ew_in import
  peer 130.40.255.29 route-policy anet_ew_out export
 #
 ipv4-family vpn-instance MSS-SN_A
  peer 31.210.66.42 as-number 65159
  peer 31.210.66.42 description TO-MSS-1
  peer 31.210.66.42 bfd min-tx-interval 100 min-rx-interval 100
  peer 31.210.66.42 bfd enable
  peer 31.210.66.42 next-hop-local
  peer 130.40.255.193 as-number 9929
  peer 130.40.255.193 description TO-MSS-1
  peer 130.40.255.193 bfd min-tx-interval 100 min-rx-interval 100
  peer 130.40.255.193 bfd enable
  peer 130.40.255.193 route-policy anet import
 #
 ipv4-family vpn-instance MVSP_NMS_B
  peer 31.92.235.23 as-number 38351
  peer 31.92.235.23 description ER-VPN-MVSP_NMS
  peer 31.92.235.23 bfd min-tx-interval 100 min-rx-interval 100
  peer 31.92.235.23 bfd enable
  peer 31.92.235.23 route-policy rp_MVSP_NMS_out export
  peer ***********18 as-number 65159
  peer ***********18 description ER-VPN-MVSP_NMS
  peer ***********18 bfd min-tx-interval 100 min-rx-interval 100
  peer ***********18 bfd enable
  peer ***********18 next-hop-local
 #
 ipv4-family vpn-instance MVSP_VS_B
  peer 31.92.236.23 as-number 38351
  peer 31.92.236.23 description ER-VPN-MVSP_VS
  peer 31.92.236.23 bfd min-tx-interval 100 min-rx-interval 100
  peer 31.92.236.23 bfd enable
  peer 31.92.236.23 route-policy rp_MVSP_VS_out export
  peer ***********14 as-number 65159
  peer ***********14 description ER-VPN-MVSP_VS
  peer ***********14 bfd min-tx-interval 100 min-rx-interval 100
  peer ***********14 bfd enable
  peer ***********14 next-hop-local
 #
 ipv4-family vpn-instance NCRP_ZZRP_A
  peer 31.210.66.46 as-number 65159
  peer 31.210.66.46 description NCRP_ZZRP
  peer 31.210.66.46 bfd min-tx-interval 100 min-rx-interval 100
  peer 31.210.66.46 bfd enable
  peer 31.210.66.46 next-hop-local
  peer 58.88.191.25 as-number 9929
  peer 58.88.191.25 description NCRP_ZZRP
  peer 58.88.191.25 bfd min-tx-interval 100 min-rx-interval 100
  peer 58.88.191.25 bfd enable
  peer 58.88.191.25 ip-prefix NCRP_ZZRP import
  peer 58.88.191.25 ip-prefix NCRP_ZZRP export
 #
 ipv4-family vpn-instance NKFVPN_B
  peer 31.34.86.23 as-number 38351
  peer 31.34.86.23 description ER-VPN-NKFVPN
  peer 31.34.86.23 bfd enable
  peer 31.34.86.23 route-policy rp_NKFVPN_out export
  peer ***********42 as-number 65159
  peer ***********42 description ER-VPN-NKFVPN
  peer ***********42 bfd min-tx-interval 100 min-rx-interval 100
  peer ***********42 bfd enable
  peer ***********42 next-hop-local
 #
 ipv4-family vpn-instance OSS-EW_A
  peer ***********6 as-number 65159
  peer ***********6 description TO-OSS-EW-1
  peer ***********6 bfd min-tx-interval 100 min-rx-interval 100
  peer ***********6 bfd enable
  peer ***********6 next-hop-local
  peer 130.40.255.21 as-number 9929
  peer 130.40.255.21 description TO-OSS-EW-1
  peer 130.40.255.21 bfd min-tx-interval 500 min-rx-interval 500
  peer 130.40.255.21 bfd enable
  peer 130.40.255.21 route-policy anet_ew_in import
  peer 130.40.255.21 route-policy anet_ew_out export
 #
 ipv4-family vpn-instance OSS-SN_A
  network ************ ***************
  network ************* ***************
  network ************** ***************
  network ************* 255.255.185.80
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************* **************
  network ************ ***************
  network ************** ***************
  network ************** ***************
  network *********** ***************
  network *********** ***************
  network *********** ***************
  network *********** ***************
  network *********** ***************
  network *********** ***************
  network ************ ***************
  peer ***********4 as-number 65159
  peer ***********4 description TO-0SS-1
  peer ***********4 bfd min-tx-interval 100 min-rx-interval 100
  peer ***********4 bfd enable
  peer ***********4 next-hop-local
  peer ************** as-number 9929
  peer ************** description TO-0SS-1
  peer ************** bfd min-tx-interval 100 min-rx-interval 100
  peer ************** bfd enable
  peer ************** route-policy denydc7 import
  peer ************** route-policy to_OSS-SN_A export
 #
 ipv4-family vpn-instance OSS_JT_B
  peer ***********22 as-number 65159
  peer ***********22 description To-HAZZ-ZYL-N40E-E1
  peer ***********22 bfd min-tx-interval 100 min-rx-interval 100
  peer ***********22 bfd enable
  peer ***********22 next-hop-local
  peer ************* as-number 38351
  peer ************* description To-HAZZ-ZYL-N40E-E1
  peer ************* bfd min-tx-interval 100 min-rx-interval 100
  peer ************* bfd enable
  peer ************* route-policy denydc7 import
  peer ************* route-policy rp_OSS_JT_out export
 #
 ipv4-family vpn-instance OTN_NMS_JT_B
  peer ************ as-number 65159
  peer ************ description OTN_NMS_GJ
  peer ************ bfd min-tx-interval 100 min-rx-interval 100
  peer ************ bfd enable
  peer ************ next-hop-local
  peer ************ as-number 38351
  peer ************ description OTN_NMS_GJ
  peer ************ bfd min-tx-interval 150 min-rx-interval 150
  peer ************ bfd enable
  peer ************ ip-prefix otn-nms import
  peer ************ route-policy rp_OTN_NMS_JT_out export
 #
 ipv4-family vpn-instance SDONT_CQ_ZX_B
  peer ************4 as-number 65159
  peer ************4 description TO-SDOTN_CQ_ZX
  peer ************4 bfd min-tx-interval 100 min-rx-interval 100
  peer ************4 bfd enable
  peer ************4 next-hop-local
  peer ************ as-number 38351
  peer ************ description TO-SDOTN_CQ_ZX
  peer ************ bfd min-tx-interval 150 min-rx-interval 150
  peer ************ bfd enable
  peer ************ ip-prefix sdotn_cq import
  peer ************ route-policy rp_SDOTN_CONTR_out export
 #
 ipv4-family vpn-instance SDOTN_AH_ZX_B
  peer ***********46 as-number 65159
  peer ***********46 description SDOTN_AH_ZX
  peer ***********46 bfd min-tx-interval 100 min-rx-interval 100
  peer ***********46 bfd enable
  peer ***********46 next-hop-local
  peer ************3 as-number 38351
  peer ************3 description SDOTN_AH_ZX
  peer ************3 bfd min-tx-interval 150 min-rx-interval 150
  peer ************3 bfd enable
  peer ************3 route-policy rp_SDOTN_CONTR_out export
 #
 ipv4-family vpn-instance SDOTN_FJ_ZX_B
  peer ************0 as-number 65159
  peer ************0 description SDOTN_FJ_ZX
  peer ************0 bfd min-tx-interval 100 min-rx-interval 100
  peer ************0 bfd enable
  peer ************0 next-hop-local
  peer ************5 as-number 38351
  peer ************5 description SDOTN_FJ_ZX
  peer ************5 bfd min-tx-interval 150 min-rx-interval 150
  peer ************5 bfd enable
  peer ************5 route-policy rp_SDOTN_CONTR_out export
 #
 ipv4-family vpn-instance SDOTN_GS_HW_B
  peer ************* as-number 65159
  peer ************* description TO-SDOTN_GS_HW
  peer ************* bfd min-tx-interval 100 min-rx-interval 100
  peer ************* bfd enable
  peer ************* next-hop-local
  peer ************* as-number 38351
  peer ************* description TO-SDOTN_GS_HW
  peer ************* bfd min-tx-interval 150 min-rx-interval 150
  peer ************* bfd enable
  peer ************* ip-prefix sdotn_gs_hw import
  peer ************* route-policy rp_SDOTN_CONTR_out export
 #
 ipv4-family vpn-instance SDOTN_GX_ZX_B
  peer ************* as-number 65159
  peer ************* description TO-SDOTN_GX_ZX
  peer ************* bfd min-tx-interval 100 min-rx-interval 100
  peer ************* bfd enable
  peer ************* next-hop-local
  peer ************5 as-number 38351
  peer ************5 description TO-SDOTN_GX_ZX
  peer ************5 bfd min-tx-interval 150 min-rx-interval 150
  peer ************5 bfd enable
  peer ************5 route-policy rp_SDOTN_CONTR_out export
 #
 ipv4-family vpn-instance SDOTN_HB_ZX_B
  peer ***********62 as-number 65159
  peer ***********62 description TO-SDOTN_HuBei_ZX
  peer ***********62 bfd min-tx-interval 100 min-rx-interval 100
  peer ***********62 bfd enable
  peer ***********62 next-hop-local
  peer 130.40.188.9 as-number 38351
  peer 130.40.188.9 description TO-SDOTN_HuBei_ZX
  peer 130.40.188.9 bfd min-tx-interval 150 min-rx-interval 150
  peer 130.40.188.9 bfd enable
  peer 130.40.188.9 route-policy rp_SDOTN_CONTR_out export
 #
 ipv4-family vpn-instance SDOTN_HEN_ZX_B
  peer ***********66 as-number 65159
  peer ***********66 description TO-SDOTN_HEN_ZX
  peer ***********66 bfd min-tx-interval 100 min-rx-interval 100
  peer ***********66 bfd enable
  peer ***********66 next-hop-local
  peer ************7 as-number 38351
  peer ************7 description TO-SDOTN_HEN_ZX
  peer ************7 bfd min-tx-interval 150 min-rx-interval 150
  peer ************7 bfd enable
  peer ************7 ip-prefix SDOTN_HeN_ZX import
  peer ************7 route-policy rp_SDOTN_CONTR_out export
 #
 ipv4-family vpn-instance SDOTN_HN_ZX_B
  peer ***********82 as-number 65159
  peer ***********82 description TO-SDOTN_HN_ZX
  peer ***********82 bfd min-tx-interval 100 min-rx-interval 100
  peer ***********82 bfd enable
  peer ***********82 next-hop-local
  peer 130.40.188.57 as-number 38351
  peer 130.40.188.57 description TO-SDOTN_HN_ZX
  peer 130.40.188.57 bfd min-tx-interval 150 min-rx-interval 150
  peer 130.40.188.57 bfd enable
  peer 130.40.188.57 ip-prefix SDOTN_HN_ZX import
  peer 130.40.188.57 route-policy rp_SDOTN_CONTR_out export
 #
 ipv4-family vpn-instance SDOTN_JT_B
  peer ************ as-number 65159
  peer ************ description SDOTN_JT
  peer ************ bfd min-tx-interval 100 min-rx-interval 100
  peer ************ bfd enable
  peer ************ next-hop-local
  peer 130.40.255.15 as-number 38351
  peer 130.40.255.15 description SDOTN_JT
  peer 130.40.255.15 bfd min-tx-interval 100 min-rx-interval 100
  peer 130.40.255.15 bfd enable
  peer 130.40.255.15 ip-prefix sdotn_jt import
  peer 130.40.255.15 route-policy rp_SDOTN_JT_out export
 #
 ipv4-family vpn-instance SDOTN_JX_HW_B
  peer ***********02 as-number 65159
  peer ***********02 description TO-SDOTN_JX_HW
  peer ***********02 bfd min-tx-interval 100 min-rx-interval 100
  peer ***********02 bfd enable
  peer ***********02 next-hop-local
  peer 130.40.188.97 as-number 38351
  peer 130.40.188.97 description TO-SDOTN_JX_HW
  peer 130.40.188.97 bfd min-tx-interval 150 min-rx-interval 150
  peer 130.40.188.97 bfd enable
  peer 130.40.188.97 route-policy rp_SDOTN_CONTR_out export
 #
 ipv4-family vpn-instance SDOTN_JX_ZX_B
  peer ***********70 as-number 65159
  peer ***********70 description TO-SDOTN_JX_ZX
  peer ***********70 bfd min-tx-interval 100 min-rx-interval 100
  peer ***********70 bfd enable
  peer ***********70 next-hop-local
  peer ************5 as-number 38351
  peer ************5 description TO-SDOTN_JX_ZX
  peer ************5 bfd min-tx-interval 150 min-rx-interval 150
  peer ************5 bfd enable
  peer ************5 route-policy rp_SDOTN_CONTR_out export
 #
 ipv4-family vpn-instance SDOTN_LN_ZX_B
  peer ***********78 as-number 65159
  peer ***********78 description TO-SDOTN_LN_ZX
  peer ***********78 bfd min-tx-interval 100 min-rx-interval 100
  peer ***********78 bfd enable
  peer ***********78 next-hop-local
  peer 130.40.188.49 as-number 38351
  peer 130.40.188.49 description TO-SDOTN_LN_ZX
  peer 130.40.188.49 bfd min-tx-interval 150 min-rx-interval 150
  peer 130.40.188.49 bfd enable
  peer 130.40.188.49 ip-prefix sdotn_ln_zx import
  peer 130.40.188.49 route-policy rp_SDOTN_CONTR_out export
 #
 ipv4-family vpn-instance SDOTN_SC_ZX_B
  peer ***********90 as-number 65159
  peer ***********90 description TO-SDOTN_SC_ZX
  peer ***********90 bfd min-tx-interval 100 min-rx-interval 100
  peer ***********90 bfd enable
  peer ***********90 next-hop-local
  peer ************3 as-number 38351
  peer ************3 description TO-SDOTN_SC_ZX
  peer ************3 bfd min-tx-interval 150 min-rx-interval 150
  peer ************3 bfd enable
  peer ************3 ip-prefix sdotn_sc import
  peer ************3 route-policy rp_SDOTN_CONTR_out export
 #
 ipv4-family vpn-instance SDOTN_SX_ZX_B
  peer ***********74 as-number 65159
  peer ***********74 description TO-SDOTN_SX_ZX
  peer ***********74 bfd min-tx-interval 100 min-rx-interval 100
  peer ***********74 bfd enable
  peer ***********74 next-hop-local
  peer 130.40.188.41 as-number 38351
  peer 130.40.188.41 description TO-SDOTN_SX_ZX
  peer 130.40.188.41 bfd min-tx-interval 150 min-rx-interval 150
  peer 130.40.188.41 bfd enable
  peer 130.40.188.41 route-policy rp_SDOTN_CONTR_out export
 #
 ipv4-family vpn-instance SDOTN_TEMP_B
  peer ***********4 as-number 65159
  peer ***********4 description vpn-SDOT_TEMP
  peer ***********4 bfd min-tx-interval 100 min-rx-interval 100
  peer ***********4 bfd enable
  peer ***********4 next-hop-local
  peer 130.50.76.225 as-number 38351
  peer 130.50.76.225 description vpn-SDOT_TEMP
  peer 130.50.76.225 bfd min-tx-interval 100 min-rx-interval 100
  peer 130.50.76.225 bfd enable
  peer 130.50.76.225 ip-prefix SDOT_TEMP import
  peer 130.50.76.225 route-policy rp_SDOTN_CONTR_out export
 #
 ipv4-family vpn-instance SDOTN_XZ_HW_B
  peer ************* as-number 65159
  peer ************* description TO-SDOTN_XZ_HW
  peer ************* bfd min-tx-interval 100 min-rx-interval 100
  peer ************* bfd enable
  peer ************* next-hop-local
  peer ************** as-number 38351
  peer ************** description TO-SDOTN_XZ_HW
  peer ************** bfd min-tx-interval 150 min-rx-interval 150
  peer ************** bfd enable
  peer ************** route-policy rp_SDOTN_CONTR_out export
 #
 ipv4-family vpn-instance SDOTN_XZ_ZX_B
  peer ************* as-number 65159
  peer ************* description TO-SDOTN_XZ_ZX
  peer ************* bfd min-tx-interval 100 min-rx-interval 100
  peer ************* bfd enable
  peer ************* next-hop-local
  peer ************ as-number 38351
  peer ************ description TO-SDOTN_XZ_ZX
  peer ************ bfd min-tx-interval 150 min-rx-interval 150
  peer ************ bfd enable
  peer ************ route-policy rp_SDOTN_CONTR_out export
 #
 ipv4-family vpn-instance SDOTN_YN_HW_B
  peer ************* as-number 65159
  peer ************* description TO-SDOTN_YN_HW
  peer ************* bfd min-tx-interval 100 min-rx-interval 100
  peer ************* bfd enable
  peer ************* next-hop-local
  peer ************05 as-number 38351
  peer ************05 description TO-SDOTN_YN_HW
  peer ************05 bfd min-tx-interval 150 min-rx-interval 150
  peer ************05 bfd enable
  peer ************05 ip-prefix sdotn_yn_hw import
  peer ************05 route-policy rp_SDOTN_CONTR_out export
 #
 ipv4-family vpn-instance SDOTN_YN_ZX_B
  peer ************* as-number 65159
  peer ************* description TO-SDOTN_YN_ZX
  peer ************* bfd min-tx-interval 100 min-rx-interval 100
  peer ************* bfd enable
  peer ************* next-hop-local
  peer ************* as-number 38351
  peer ************* description TO-SDOTN_YN_ZX
  peer ************* bfd min-tx-interval 150 min-rx-interval 150
  peer ************* bfd enable
  peer ************* route-policy rp_SDOTN_CONTR_out export
 #
 ipv4-family vpn-instance SMAN_NMS_CJ_A
  peer *********** as-number 65159
  peer *********** description SMAN_NMS_CJ
  peer *********** bfd min-tx-interval 100 min-rx-interval 100
  peer *********** bfd enable
  peer *********** next-hop-local
  peer ************ as-number 9929
  peer ************ description SMAN_NMS_CJ
  peer ************ bfd min-tx-interval 100 min-rx-interval 100
  peer ************ bfd enable
 #
 ipv4-family vpn-instance SMAN_NMS_JT_A
  peer *********** as-number 65159
  peer *********** description SMAN_NMS_JT
  peer *********** bfd min-tx-interval 100 min-rx-interval 100
  peer *********** bfd enable
  peer *********** next-hop-local
  peer ************ as-number 9929
  peer ************ description SMAN_NMS_JT
  peer ************ bfd min-tx-interval 100 min-rx-interval 100
  peer ************ bfd enable
 #
 ipv4-family vpn-instance TAC_DATA_A
  peer ************ as-number 65159
  peer ************ description AnQuanGuanLi-1
  peer ************ bfd min-tx-interval 100 min-rx-interval 100
  peer ************ bfd enable
  peer ************ next-hop-local
  peer ************** as-number 9929
  peer ************** description AnQuanGuanLi-1
  peer ************** bfd min-tx-interval 100 min-rx-interval 100
  peer ************** bfd enable
 #
 ipv4-family vpn-instance TAC_VSS_A
  peer ************ as-number 65159
  peer ************ description AnQuanLouSao
  peer ************ bfd min-tx-interval 100 min-rx-interval 100
  peer ************ bfd enable
  peer ************ next-hop-local
  peer ************** as-number 9929
  peer ************** description AnQuanLouSao
  peer ************** bfd min-tx-interval 100 min-rx-interval 100
  peer ************** bfd enable
  peer ************** ip-prefix lousao import
  peer ************** ip-prefix lousao export
 #
 ipv4-family vpn-instance UTC_MGMN_B
  peer ************ as-number 38351
  peer ************ description UTC_MGMN
  peer ************ bfd min-tx-interval 150 min-rx-interval 150
  peer ************ bfd enable
  peer ************ ip-prefix UTC_MGMN import
  peer ************ route-policy rp_UTC_MGMN_out export
  peer ************ as-number 65159
  peer ************ description UTC_MGMN
  peer ************ bfd min-tx-interval 100 min-rx-interval 100
  peer ************ bfd enable
  peer ************ next-hop-local
 #
 ipv4-family vpn-instance VRM-H_A
  peer ************ as-number 65159
  peer ************ description VR_MGMT
  peer ************ bfd min-tx-interval 100 min-rx-interval 100
  peer ************ bfd enable
  peer ************ next-hop-local
  peer ************* as-number 9929
  peer ************* description VR_MGMT
  peer ************* bfd min-tx-interval 100 min-rx-interval 100
  peer ************* bfd enable
  peer ************* ip-prefix untrust_VR_MGMT import
  peer ************* ip-prefix untrust_VR_MGMT export
 #
 ipv4-family vpn-instance ZNZX-NMS_A
  peer *********** as-number 9929
  peer *********** description ZNZX-NMS
  peer *********** bfd min-tx-interval 100 min-rx-interval 100
  peer *********** bfd enable
  peer *********** ip-prefix znzx_nms import
  peer *********** ip-prefix znzx_nms export
  peer *********** as-number 65159
  peer *********** description ZNZX-NMS
  peer *********** bfd min-tx-interval 100 min-rx-interval 100
  peer *********** bfd enable
  peer *********** next-hop-local
 #
 ipv4-family vpn-instance ZNZX_A
  peer 31.80.39.23 as-number 9929
  peer 31.80.39.23 description ZNZX
  peer 31.80.39.23 bfd min-tx-interval 100 min-rx-interval 100
  peer 31.80.39.23 bfd enable
  peer 31.80.39.23 ip-prefix znzx import
  peer 31.80.39.23 ip-prefix znzx export
  peer ************ as-number 65159
  peer ************ description ZNZX
  peer ************ bfd min-tx-interval 100 min-rx-interval 100
  peer ************ bfd enable
  peer ************ next-hop-local
 #
 ipv4-family vpn-instance ha_dhtsgz_A
  peer ***********0 as-number 65159
  peer ***********0 description ha_dhtsgz
  peer ***********0 bfd min-tx-interval 100 min-rx-interval 100
  peer ***********0 bfd enable
  peer ***********0 next-hop-local
  peer 130.40.255.209 as-number 9929
  peer 130.40.255.209 description ha_dhtsgz
  peer 130.40.255.209 bfd min-tx-interval 100 min-rx-interval 100
  peer 130.40.255.209 bfd enable
  peer 130.40.255.209 ip-prefix ha_dhtsgz import
  peer 130.40.255.209 ip-prefix ha_dhtsgz export
 #
 ipv4-family vpn-instance vrf_169
  network ***********
  network 150.192.95.12 ***************
  peer ************4 as-number 65159
  peer ************4 description To_169
  peer ************4 bfd min-tx-interval 100 min-rx-interval 100
  peer ************4 bfd enable
  peer ************4 next-hop-local
 #
 ipv4-family vpn-instance vrf_406
  network 130.48.180.80 **************
  peer ***********02 as-number 65159
  peer ***********02 description To_406
  peer ***********02 bfd min-tx-interval 100 min-rx-interval 100
  peer ***********02 bfd enable
  peer ***********02 next-hop-local
 #
 ipv6-family vpnv6
  policy vpn-target
  peer ************* enable
  peer ************* reflect-client
  peer ************* next-hop-local
 #
 ipv6-family vpn-instance 5GC_MGMN_B
  peer 2408:81B0:7000::5:1000:105D as-number 65159
  peer 2408:81B0:7000::5:1000:105D description To-VPN-5GC-MGMN
  peer 2408:81B0:7000::5:1000:105D bfd min-tx-interval 150 min-rx-interval 150
  peer 2408:81B0:7000::5:1000:105D bfd enable
  peer 2408:81B0:7000::5:1000:105D next-hop-local
  peer 2408:81B0:7000::5:1002:6 as-number 38351
  peer 2408:81B0:7000::5:1002:6 description To-VPN-5GC-MGMN
  peer 2408:81B0:7000::5:1002:6 bfd min-tx-interval 150 min-rx-interval 150
  peer 2408:81B0:7000::5:1002:6 bfd enable
  peer 2408:81B0:7000::5:1002:6 ipv6-prefix 5gc_mgmn import
  peer 2408:81B0:7000::5:1002:6 ipv6-prefix 5gc_mgmn export
 #
 ipv6-family vpn-instance 5GC_NEF_B
  peer 2408:8140:C0FF:F900::1:2 as-number 38351
  peer 2408:8140:C0FF:F900::1:2 description TO-5GC_NEF
  peer 2408:8140:C0FF:F900::1:2 bfd min-tx-interval 100 min-rx-interval 100
  peer 2408:8140:C0FF:F900::1:2 bfd enable
  peer 2408:8140:C0FF:F900::1:2 ipv6-prefix 5gc_nef import
  peer 2408:8140:C0FF:F900::1:2 ipv6-prefix 5gc_nef export
  peer 2408:81B0:7000::5:1000:1067 as-number 65159
  peer 2408:81B0:7000::5:1000:1067 description TO-5GC_NEF
  peer 2408:81B0:7000::5:1000:1067 bfd min-tx-interval 150 min-rx-interval 150
  peer 2408:81B0:7000::5:1000:1067 bfd enable
  peer 2408:81B0:7000::5:1000:1067 next-hop-local
 #
 ipv6-family vpn-instance 5GC_Test_B
  peer 2408:81B0:A00:1:F000::2 as-number 38351
  peer 2408:81B0:A00:1:F000::2 description TO-5GC_TEST
  peer 2408:81B0:A00:1:F000::2 bfd min-tx-interval 150 min-rx-interval 150
  peer 2408:81B0:A00:1:F000::2 bfd enable
  peer 2408:81B0:A00:1:F000::2 route-policy rp_5gc_test_in import
  peer 2408:81B0:A00:1:F000::2 route-policy rp_5gc_test_out export
  peer 2408:81B0:7000::5:1000:1071 as-number 65159
  peer 2408:81B0:7000::5:1000:1071 description TO-5GC_TEST
  peer 2408:81B0:7000::5:1000:1071 bfd min-tx-interval 150 min-rx-interval 150
  peer 2408:81B0:7000::5:1000:1071 bfd enable
  peer 2408:81B0:7000::5:1000:1071 next-hop-local
 #
 ipv6-family vpn-instance 5G_RCS_B
  peer 2408:81B0:7000::5:1000:1069 as-number 65159
  peer 2408:81B0:7000::5:1000:1069 description 5G_RCS
  peer 2408:81B0:7000::5:1000:1069 bfd min-tx-interval 150 min-rx-interval 150
  peer 2408:81B0:7000::5:1000:1069 bfd enable
  peer 2408:81B0:7000::5:1000:1069 next-hop-local
  peer 2408:81B0:7000::5:1002:A as-number 38351
  peer 2408:81B0:7000::5:1002:A description 5G_RCS
  peer 2408:81B0:7000::5:1002:A bfd min-tx-interval 150 min-rx-interval 150
  peer 2408:81B0:7000::5:1002:A bfd enable
  peer 2408:81B0:7000::5:1002:A ipv6-prefix 5gc_rcs_in import
  peer 2408:81B0:7000::5:1002:A ipv6-prefix 5gc_rcs_out export
 #
 ipv6-family vpn-instance MVSP_VS_B
  peer 2408:81B0:7000::5:1000:104F as-number 65159
  peer 2408:81B0:7000::5:1000:104F description MVSP_VS_TO_FW
  peer 2408:81B0:7000::5:1000:104F bfd min-tx-interval 150 min-rx-interval 150
  peer 2408:81B0:7000::5:1000:104F bfd enable
  peer 2408:81B0:7000::5:1000:104F next-hop-local
  peer 2408:81B0:7000::5:1002:10 as-number 38351
  peer 2408:81B0:7000::5:1002:10 description MVSP_VS_TO_B
  peer 2408:81B0:7000::5:1002:10 bfd min-tx-interval 150 min-rx-interval 150
  peer 2408:81B0:7000::5:1002:10 bfd enable
  peer 2408:81B0:7000::5:1002:10 ipv6-prefix mvsp_vs_in import
 #
 ipv6-family vpn-instance UTC_MGMN_B
  peer 2408:81B0:7000::5:1000:106B as-number 65159
  peer 2408:81B0:7000::5:1000:106B description UTC_MGMN
  peer 2408:81B0:7000::5:1000:106B bfd min-tx-interval 150 min-rx-interval 150
  peer 2408:81B0:7000::5:1000:106B bfd enable
  peer 2408:81B0:7000::5:1000:106B next-hop-local
  peer 2408:81B0:7000::5:1002:2 as-number 38351
  peer 2408:81B0:7000::5:1002:2 description UTC_MGMN
  peer 2408:81B0:7000::5:1002:2 bfd min-tx-interval 150 min-rx-interval 150
  peer 2408:81B0:7000::5:1002:2 bfd enable
  peer 2408:81B0:7000::5:1002:2 ipv6-prefix utc_mgmn import
  peer 2408:81B0:7000::5:1002:2 ipv6-prefix utc_mgmn export
 #
 ipv6-family vpn-instance vrf_169
  network :: 0
  network 2408:8001:680:8000::BD2 127
  peer 2408:81B0:7000::5:1000:1D as-number 65159
  peer 2408:81B0:7000::5:1000:1D description To_169
  peer 2408:81B0:7000::5:1000:1D bfd min-tx-interval 150 min-rx-interval 150
  peer 2408:81B0:7000::5:1000:1D bfd enable
  peer 2408:81B0:7000::5:1000:1D next-hop-local
#
ospf 10 router-id *************
 bfd all-interfaces enable
 bfd all-interfaces min-tx-interval 50 min-rx-interval 50
 opaque-capability enable
 area ***********
  network ************ ***********
  network ************* ***********
#
undo dcn
#
route-policy IMS_VPN_out permit node 10
 if-match ip-prefix pl_IMS_VPN
#
route-policy add_med permit node 10
#
route-policy anet deny node 10
 if-match ip-prefix anet
#
route-policy anet permit node 20
#
route-policy anet_ew_in deny node 10
 if-match ip-prefix anet_ew_in
#
route-policy anet_ew_in permit node 20
#
route-policy anet_ew_out permit node 10
 if-match ip-prefix anet_ew_out
#
route-policy bnet_import_direct permit node 10
 if-match ip-prefix bnet_import_direct
 apply tag 300
#
route-policy denydc7 deny node 10
 if-match ip-prefix dc7
#
route-policy denydc7 permit node 40
#
route-policy rp_5GC_MGMN_out permit node 10
 if-match ip-prefix pl_5GC_MGMN
#
route-policy rp_5G_MEC_in permit node 10
 if-match ip-prefix pl_5G_MEC
#
route-policy rp_5G_MEC_out permit node 10
 if-match ip-prefix pl_5G_MEC
#
route-policy rp_5G_RCS_out permit node 10
 if-match ip-prefix pl_5G_RCS
#
route-policy rp_5gc_test_in permit node 10
 if-match ipv6 address prefix-list 5gc_test_in
#
route-policy rp_5gc_test_out permit node 10
 if-match ipv6 address prefix-list 5gc_test_out
#
route-policy rp_CN_MGMN_in permit node 10
 if-match ip-prefix p1_CN_MGMN_in
#
route-policy rp_CN_MGMN_out permit node 10
 if-match ip-prefix p1_CN_MGMN_out
#
route-policy rp_CU_OTNCPE_SDN_out permit node 10
 if-match ip-prefix pl_CU_OTNCPE_SDN
#
route-policy rp_IOTPT_ZW_out permit node 10
 if-match ip-prefix pl_IOTPT_ZW
#
route-policy rp_IOT_BSS_out permit node 10
 if-match ip-prefix pl_IOT_BSS
#
route-policy rp_IPRAN_JT_out permit node 10
 if-match ip-prefix pl_IPRAN_JT
#
route-policy rp_IPRAN_NMS_JT_out permit node 10
 if-match ip-prefix pl_IPRAN_NMS_JT
#
route-policy rp_IoT_OSS_out permit node 10
 if-match ip-prefix pl_IoT_OSS
#
route-policy rp_MVSP_NMS_out permit node 10
 if-match ip-prefix pl_MVSP_NMS
#
route-policy rp_MVSP_VS_out permit node 10
 if-match ip-prefix pl_MVSP_VS
#
route-policy rp_NKFVPN_out permit node 10
 if-match ip-prefix pl_NKFVPN
#
route-policy rp_OSS_JT_out permit node 10
 if-match ip-prefix pl_OSS_JT
#
route-policy rp_OTN_NMS_JT_out permit node 10
 if-match ip-prefix pl_OTN_NMS_JT
#
route-policy rp_SDOTN_CONTR_out permit node 10
 if-match ip-prefix pl_SDOTN_CONTR
#
route-policy rp_SDOTN_JT_out permit node 10
 if-match ip-prefix pl_SDOTN_JT
#
route-policy rp_UTC_MGMN_out permit node 10
 if-match ip-prefix pl_UTC_MGMN
#
route-policy to_BSS-SN_A deny node 5
 if-match ip-prefix BSS-SN_A_Network
#
route-policy to_BSS-SN_A permit node 10
#
route-policy to_OSS-SN_A deny node 5
 if-match ip-prefix OSS-SN_A_Network
#
route-policy to_OSS-SN_A permit node 10
#
ip ip-prefix 5gc_nef index 10 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix 5gc_nef index 20 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix 5gc_nef index 30 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix 5gc_nef index 40 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix 5gc_nef index 50 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix 5gc_tanzhen_in index 10 deny *********** 16 greater-equal 16 less-equal 24
ip ip-prefix 5gc_tanzhen_in index 20 permit *********** 12 greater-equal 16 less-equal 24
ip ip-prefix 5gc_tanzhen_in index 30 permit *********** 14 greater-equal 16 less-equal 24
ip ip-prefix 5gc_tanzhen_in index 40 permit ************ 24
ip ip-prefix 5gc_tanzhen_in index 50 permit ************ 24
ip ip-prefix 5gc_tanzhen_out index 10 permit ************** 26
ip ip-prefix BSS-SN_A_Network index 10 permit ************* 32
ip ip-prefix BSS-SN_A_Network index 20 permit ************ 32
ip ip-prefix BSS-SN_A_Network index 30 permit ************ 32
ip ip-prefix BSS-SN_A_Network index 40 permit ************ 32
ip ip-prefix BSS-SN_A_Network index 50 permit ************* 32
ip ip-prefix BSS-SN_A_Network index 60 permit ************* 32
ip ip-prefix DianLianGJGX index 10 permit ************* 24
ip ip-prefix DianLianGJGX index 20 permit ************ 24
ip ip-prefix GLW_BVMP-H_in index 10 permit ************* 24 greater-equal 24 less-equal 32
ip ip-prefix GLW_BVMP-H_in index 20 permit ************* 24 greater-equal 24 less-equal 32
ip ip-prefix IDC_CONTROL_H index 10 permit ************ 23 greater-equal 23 less-equal 32
ip ip-prefix IDC_CONTROL_H index 20 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix IDC_CONTROL_H index 30 permit ************ 21 greater-equal 21 less-equal 32
ip ip-prefix IDC_CONTROL_H index 40 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix IDC_CONTROL_H index 50 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix IDC_CONTROL_H index 60 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix IDC_CONTROL_H index 70 permit ************ 24
ip ip-prefix IDC_CONTROL_H index 80 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix IDC_CONTROL_H index 90 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix IDC_CONTROL_H index 100 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix IDC_CONTROL_H index 120 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix IDC_CONTROL_H index 130 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix IDC_CONTROL_H index 140 permit ************ 24
ip ip-prefix IOT_BSS index 10 permit ************* 24 greater-equal 24 less-equal 32
ip ip-prefix IOT_BSS index 20 permit ************* 24 greater-equal 24 less-equal 32
ip ip-prefix IOT_BSS index 30 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix IOT_BSS index 40 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix IOT_BSS index 50 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix IOT_BSS index 60 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix IOT_BSS index 70 permit ************* 24 greater-equal 24 less-equal 32
ip ip-prefix IOT_BSS index 80 permit ************* 24 greater-equal 24 less-equal 32
ip ip-prefix IOT_BSS index 90 permit ************* 24 greater-equal 24 less-equal 32
ip ip-prefix IOT_BSS index 100 permit ************* 24 greater-equal 24 less-equal 32
ip ip-prefix IOT_BSS index 110 permit ************* 24 greater-equal 24 less-equal 32
ip ip-prefix IOT_BSS index 120 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix IOT_BSS index 130 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix IOT_BSS index 140 permit ************* 24 greater-equal 24 less-equal 32
ip ip-prefix IOT_BSS index 150 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix IOT_BSS index 160 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix IOT_BSS index 170 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix IOT_BSS index 180 permit ************* 24 greater-equal 24 less-equal 32
ip ip-prefix IOT_BSS index 190 permit ************* 24 greater-equal 24 less-equal 32
ip ip-prefix IOT_BSS index 200 permit ************* 24 greater-equal 24 less-equal 32
ip ip-prefix IOT_BSS index 210 permit *********** 32
ip ip-prefix IOT_BSS index 220 permit ************ 32
ip ip-prefix IOT_BSS index 230 permit ************ 32
ip ip-prefix IOT_BSS index 240 permit ************ 32
ip ip-prefix IOT_BSS index 250 permit ************* 24 greater-equal 24 less-equal 32
ip ip-prefix IOT_BSS index 260 permit ************* 24 greater-equal 24 less-equal 32
ip ip-prefix NCRP_ZZRP index 10 permit ************** 27 greater-equal 27 less-equal 32
ip ip-prefix NCRP_ZZRP index 20 permit *********** 24 greater-equal 24 less-equal 32
ip ip-prefix NCRP_ZZRP index 30 permit *********** 24 greater-equal 24 less-equal 32
ip ip-prefix NCRP_ZZRP index 40 permit *********** 24 greater-equal 24 less-equal 32
ip ip-prefix NCRP_ZZRP index 50 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix NCRP_ZZRP index 60 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix NCRP_ZZRP index 70 permit *********** 24 greater-equal 24 less-equal 32
ip ip-prefix NCRP_ZZRP index 80 permit *********** 24 greater-equal 24 less-equal 24
ip ip-prefix NCRP_ZZRP index 90 permit *********** 24 greater-equal 24 less-equal 24
ip ip-prefix NCRP_ZZRP index 100 permit ************ 23 greater-equal 23 less-equal 24
ip ip-prefix NCRP_ZZRP index 110 permit ************ 23 greater-equal 23 less-equal 24
ip ip-prefix NCRP_ZZRP index 120 permit ************* 24
ip ip-prefix NCRP_ZZRP index 130 permit ************* 23
ip ip-prefix NCRP_ZZRP index 140 permit ************* 23
ip ip-prefix NCRP_ZZRP index 150 permit ************ 24
ip ip-prefix NCRP_ZZRP index 160 permit ************ 24
ip ip-prefix NCRP_ZZRP index 170 permit ************ 24
ip ip-prefix NCRP_ZZRP index 180 permit ************ 24
ip ip-prefix NCRP_ZZRP index 190 permit ************ 24
ip ip-prefix NCRP_ZZRP index 200 permit ************ 24
ip ip-prefix OSS-SN_A_Network index 10 permit ************ 32
ip ip-prefix OSS-SN_A_Network index 20 permit ************* 32
ip ip-prefix OSS-SN_A_Network index 30 permit ************** 32
ip ip-prefix OSS-SN_A_Network index 40 permit ************* 21
ip ip-prefix OSS-SN_A_Network index 50 permit ************* 24
ip ip-prefix OSS-SN_A_Network index 60 permit ************* 23
ip ip-prefix OSS-SN_A_Network index 70 permit ************* 23
ip ip-prefix OSS-SN_A_Network index 80 permit ************* 24
ip ip-prefix OSS-SN_A_Network index 90 permit ************* 24
ip ip-prefix OSS-SN_A_Network index 100 permit ************* 23
ip ip-prefix OSS-SN_A_Network index 110 permit ************* 23
ip ip-prefix OSS-SN_A_Network index 120 permit ************* 24
ip ip-prefix OSS-SN_A_Network index 130 permit ************* 24
ip ip-prefix OSS-SN_A_Network index 140 permit ************* 23
ip ip-prefix OSS-SN_A_Network index 150 permit ************* 23
ip ip-prefix OSS-SN_A_Network index 160 permit ************* 24
ip ip-prefix OSS-SN_A_Network index 170 permit ************* 23
ip ip-prefix OSS-SN_A_Network index 180 permit ************* 22
ip ip-prefix OSS-SN_A_Network index 190 permit ************* 24
ip ip-prefix OSS-SN_A_Network index 200 permit ************* 23
ip ip-prefix OSS-SN_A_Network index 210 permit ************* 23
ip ip-prefix OSS-SN_A_Network index 220 permit ************* 24
ip ip-prefix OSS-SN_A_Network index 230 permit ************* 23
ip ip-prefix OSS-SN_A_Network index 240 permit ************* 22
ip ip-prefix OSS-SN_A_Network index 250 permit ************* 24
ip ip-prefix OSS-SN_A_Network index 260 permit ************* 24
ip ip-prefix OSS-SN_A_Network index 270 permit ************* 23
ip ip-prefix OSS-SN_A_Network index 280 permit ************* 24
ip ip-prefix OSS-SN_A_Network index 290 permit ************* 22
ip ip-prefix OSS-SN_A_Network index 300 permit ************* 23
ip ip-prefix OSS-SN_A_Network index 310 permit ************* 24
ip ip-prefix OSS-SN_A_Network index 320 permit ************* 24
ip ip-prefix OSS-SN_A_Network index 330 permit ************* 23
ip ip-prefix OSS-SN_A_Network index 340 permit ************* 23
ip ip-prefix OSS-SN_A_Network index 350 permit ************* 24
ip ip-prefix OSS-SN_A_Network index 360 permit ************* 23
ip ip-prefix OSS-SN_A_Network index 370 permit ************* 23
ip ip-prefix OSS-SN_A_Network index 380 permit ************* 24
ip ip-prefix OSS-SN_A_Network index 390 permit ************* 23
ip ip-prefix OSS-SN_A_Network index 400 permit ************* 23
ip ip-prefix OSS-SN_A_Network index 410 permit ************* 24
ip ip-prefix OSS-SN_A_Network index 420 permit ************* 24
ip ip-prefix OSS-SN_A_Network index 430 permit ************* 23
ip ip-prefix OSS-SN_A_Network index 440 permit ************* 22
ip ip-prefix OSS-SN_A_Network index 450 permit ************* 24
ip ip-prefix OSS-SN_A_Network index 460 permit ************* 23
ip ip-prefix OSS-SN_A_Network index 470 permit ************* 23
ip ip-prefix OSS-SN_A_Network index 480 permit ************* 24
ip ip-prefix OSS-SN_A_Network index 490 permit ************* 24
ip ip-prefix OSS-SN_A_Network index 500 permit ************* 23
ip ip-prefix OSS-SN_A_Network index 510 permit ************* 23
ip ip-prefix OSS-SN_A_Network index 520 permit ************* 24
ip ip-prefix OSS-SN_A_Network index 530 permit ************* 23
ip ip-prefix OSS-SN_A_Network index 540 permit ************* 23
ip ip-prefix OSS-SN_A_Network index 550 permit ************* 24
ip ip-prefix OSS-SN_A_Network index 560 permit ************* 23
ip ip-prefix OSS-SN_A_Network index 570 permit ************* 24
ip ip-prefix OSS-SN_A_Network index 580 permit ************* 24
ip ip-prefix OSS-SN_A_Network index 590 permit ************* 23
ip ip-prefix OSS-SN_A_Network index 600 permit ************* 23
ip ip-prefix OSS-SN_A_Network index 610 permit ************ 32
ip ip-prefix OSS-SN_A_Network index 620 permit ************** 32
ip ip-prefix OSS-SN_A_Network index 630 permit ************** 32
ip ip-prefix OSS-SN_A_Network index 640 permit *********** 32
ip ip-prefix OSS-SN_A_Network index 650 permit *********** 32
ip ip-prefix OSS-SN_A_Network index 660 permit *********** 32
ip ip-prefix OSS-SN_A_Network index 670 permit *********** 32
ip ip-prefix OSS-SN_A_Network index 680 permit *********** 32
ip ip-prefix OSS-SN_A_Network index 690 permit *********** 32
ip ip-prefix OSS-SN_A_Network index 700 permit ************ 32
ip ip-prefix SDOTN_HN_ZX index 10 permit ************* 22
ip ip-prefix SDOTN_HN_ZX index 20 permit ************* 24
ip ip-prefix SDOTN_HN_ZX index 25 permit ************ 20 greater-equal 20 less-equal 24
ip ip-prefix SDOTN_HN_ZX index 30 permit ************* 30
ip ip-prefix SDOTN_HN_ZX index 40 permit ************* 30
ip ip-prefix SDOTN_HN_ZX index 120 deny *********** 0
ip ip-prefix SDOTN_HeN_ZX index 10 permit *********** 30
ip ip-prefix SDOTN_HeN_ZX index 20 permit *********** 30
ip ip-prefix SDOTN_HeN_ZX index 30 permit ************* 23
ip ip-prefix SDOTN_HeN_ZX index 40 permit ************* 22
ip ip-prefix SDOTN_HeN_ZX index 50 permit ************* 30
ip ip-prefix SDOTN_HeN_ZX index 60 permit ************* 30
ip ip-prefix SDOTN_HeN_ZX index 70 permit ************ 17
ip ip-prefix SDOT_TEMP index 60 permit *********** 16 greater-equal 16 less-equal 32
ip ip-prefix SDOT_TEMP index 70 permit ************** 28 greater-equal 28 less-equal 32
ip ip-prefix SDOT_TEMP index 80 permit ************* 24 greater-equal 24 less-equal 24
ip ip-prefix SDOT_TEMP index 90 permit ************* 24 greater-equal 24 less-equal 24
ip ip-prefix SDOT_TEMP index 100 permit ************* 24 greater-equal 24 less-equal 24
ip ip-prefix SDOT_TEMP index 110 permit ************* 24 greater-equal 24 less-equal 24
ip ip-prefix SDOT_TEMP index 120 permit ************* 24 greater-equal 24 less-equal 24
ip ip-prefix UTC_MGMN index 80 permit *********** 24 greater-equal 24 less-equal 32
ip ip-prefix UTC_MGMN index 90 permit *********** 24 greater-equal 24 less-equal 32
ip ip-prefix UTC_MGMN index 100 permit *********** 24 greater-equal 24 less-equal 32
ip ip-prefix UTC_MGMN index 110 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix UTC_MGMN index 120 permit *********** 24 greater-equal 24 less-equal 32
ip ip-prefix UTC_MGMN index 130 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix UTC_MGMN index 140 permit *********** 16 greater-equal 16 less-equal 32
ip ip-prefix UTC_MGMN index 150 permit *********** 24 greater-equal 24 less-equal 32
ip ip-prefix UTC_MGMN index 160 permit *********** 24 greater-equal 24 less-equal 32
ip ip-prefix UTC_MGMN index 170 permit *********** 24 greater-equal 24 less-equal 32
ip ip-prefix UTC_MGMN index 180 permit *********** 24 greater-equal 24 less-equal 32
ip ip-prefix UTC_MGMN index 190 permit 39.46.84.80 24 greater-equal 24 less-equal 32
ip ip-prefix UTC_MGMN index 200 permit 39.48.38.80 24 greater-equal 24 less-equal 32
ip ip-prefix UTC_MGMN index 210 permit 39.54.64.80 24 greater-equal 24 less-equal 32
ip ip-prefix UTC_MGMN index 220 permit 39.60.55.80 24 greater-equal 24 less-equal 32
ip ip-prefix UTC_MGMN index 230 permit 39.34.38.80 24 greater-equal 24 less-equal 32
ip ip-prefix UTC_MGMN index 240 permit 39.28.64.80 24 greater-equal 24 less-equal 32
ip ip-prefix UTC_MGMN index 250 permit 39.42.64.80 24 greater-equal 24 less-equal 32
ip ip-prefix UTC_MGMN index 260 permit 39.40.64.80 24 greater-equal 24 less-equal 32
ip ip-prefix UTC_MGMN index 270 permit 39.44.64.80 24 greater-equal 24 less-equal 32
ip ip-prefix UTC_MGMN index 280 permit 39.52.64.80 24 greater-equal 24 less-equal 32
ip ip-prefix UTC_MGMN index 290 permit 39.58.64.80 24 greater-equal 24 less-equal 32
ip ip-prefix UTC_MGMN index 300 permit 39.62.38.80 24 greater-equal 24 less-equal 32
ip ip-prefix UTC_MGMN index 310 permit 39.64.64.80 24 greater-equal 24 less-equal 32
ip ip-prefix UTC_MGMN index 320 permit 39.70.64.80 24 greater-equal 24 less-equal 32
ip ip-prefix UTC_MGMN index 330 permit 39.74.64.80 24 greater-equal 24 less-equal 32
ip ip-prefix UTC_MGMN index 340 permit 39.78.64.80 24 greater-equal 24 less-equal 32
ip ip-prefix UTC_MGMN index 350 permit 39.80.64.80 24 greater-equal 24 less-equal 32
ip ip-prefix UTC_MGMN index 360 permit 39.82.64.80 24 greater-equal 24 less-equal 32
ip ip-prefix UTC_MGMN index 370 permit 39.84.64.80 24 greater-equal 24 less-equal 32
ip ip-prefix UTC_MGMN index 380 permit 39.36.64.80 24 greater-equal 24 less-equal 32
ip ip-prefix UTC_MGMN index 390 permit 39.38.64.80 24 greater-equal 24 less-equal 32
ip ip-prefix UTC_MGMN index 400 permit 39.50.64.80 24 greater-equal 24 less-equal 32
ip ip-prefix UTC_MGMN index 420 permit 39.66.64.80 24 greater-equal 24 less-equal 32
ip ip-prefix UTC_MGMN index 440 permit 39.72.64.80 16 greater-equal 16 less-equal 32
ip ip-prefix UTC_MGMN index 450 permit 39.76.64.80 24 greater-equal 24 less-equal 32
ip ip-prefix UTC_MGMN index 460 permit 39.64.56.80 24 greater-equal 24 less-equal 32
ip ip-prefix UTC_MGMN index 470 permit 39.46.64.80 20 greater-equal 20 less-equal 32
ip ip-prefix UTC_MGMN index 480 permit 39.46.53.80 20 greater-equal 20 less-equal 32
ip ip-prefix UTC_MGMN index 490 permit 39.56.64.80 16 greater-equal 16 less-equal 32
ip ip-prefix UTC_MGMN index 500 permit 39.68.64.80 16 greater-equal 16 less-equal 32
ip ip-prefix UTC_MGMN index 510 permit 39.30.45.80 24 greater-equal 24 less-equal 32
ip ip-prefix UTC_MGMN index 520 permit 39.30.165.80 24 greater-equal 24 less-equal 32
ip ip-prefix UTC_MGMN index 530 permit 39.30.173.72 28 greater-equal 28 less-equal 32
ip ip-prefix UTC_MGMN index 540 permit 39.34.47.216 28 greater-equal 28 less-equal 32
ip ip-prefix UTC_MGMN index 550 permit 39.30.173.80 23 greater-equal 23 less-equal 32
ip ip-prefix UTC_MGMN index 560 permit 39.80.64.80 11 greater-equal 24 less-equal 32
ip ip-prefix UTC_MGMN index 570 permit 39.56.64.80 12 greater-equal 24 less-equal 32
ip ip-prefix UTC_MGMN index 580 permit 39.72.64.80 13 greater-equal 24 less-equal 32
ip ip-prefix UTC_MGMN index 590 permit 39.80.64.80 14 greater-equal 24 less-equal 32
ip ip-prefix UTC_MGMN index 600 permit 39.84.64.80 15 greater-equal 24 less-equal 32
ip ip-prefix UTC_MGMN index 610 permit 39.34.43.80 24 greater-equal 24 less-equal 32
ip ip-prefix anet index 40 permit ************ 22 greater-equal 24 less-equal 24
ip ip-prefix anet index 50 permit ************ 19 greater-equal 24 less-equal 24
ip ip-prefix anet index 60 permit ************* 24 greater-equal 24 less-equal 24
ip ip-prefix anet index 70 permit ************ 24 greater-equal 24 less-equal 24
ip ip-prefix anet index 80 permit ************ 23 greater-equal 24 less-equal 24
ip ip-prefix anet index 90 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix anet index 100 permit ************* 24 greater-equal 24 less-equal 24
ip ip-prefix anet index 110 permit ************* 21 greater-equal 21 less-equal 32
ip ip-prefix anet index 120 permit ************* 23 greater-equal 23 less-equal 32
ip ip-prefix anet index 130 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix anet index 140 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix anet index 150 permit ************ 21 greater-equal 21 less-equal 32
ip ip-prefix anet index 160 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix anet index 170 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix anet index 180 permit ************* 19 greater-equal 19 less-equal 32
ip ip-prefix anet index 190 permit ************* 22 greater-equal 22 less-equal 32
ip ip-prefix anet index 200 permit ************* 19 greater-equal 19 less-equal 32
ip ip-prefix anet index 210 permit ************* 19 greater-equal 19 less-equal 32
ip ip-prefix anet index 220 permit ************ 22 greater-equal 22 less-equal 32
ip ip-prefix anet index 230 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix anet index 240 permit ************ 23 greater-equal 23 less-equal 32
ip ip-prefix anet index 250 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix anet index 260 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix anet index 270 permit ************ 20 greater-equal 20 less-equal 32
ip ip-prefix anet index 280 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix anet index 290 permit ************* 24 greater-equal 32 less-equal 32
ip ip-prefix anet index 300 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix anet index 700 permit ************* 18 greater-equal 18 less-equal 32
ip ip-prefix anet index 710 permit ************* 18 greater-equal 18 less-equal 32
ip ip-prefix anet_ew_in index 40 permit ************ 22 greater-equal 24 less-equal 24
ip ip-prefix anet_ew_in index 50 permit ************ 19 greater-equal 24 less-equal 24
ip ip-prefix anet_ew_in index 60 permit ************* 24 greater-equal 24 less-equal 24
ip ip-prefix anet_ew_in index 70 permit ************ 24 greater-equal 24 less-equal 24
ip ip-prefix anet_ew_in index 80 permit ************ 23 greater-equal 24 less-equal 24
ip ip-prefix anet_ew_in index 90 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix anet_ew_in index 100 permit ************* 24 greater-equal 24 less-equal 24
ip ip-prefix anet_ew_in index 110 permit ************* 21 greater-equal 21 less-equal 32
ip ip-prefix anet_ew_in index 120 permit ************* 23 greater-equal 23 less-equal 32
ip ip-prefix anet_ew_in index 130 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix anet_ew_in index 140 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix anet_ew_in index 150 permit ************ 21 greater-equal 21 less-equal 32
ip ip-prefix anet_ew_in index 160 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix anet_ew_in index 170 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix anet_ew_in index 180 permit ************* 19 greater-equal 19 less-equal 32
ip ip-prefix anet_ew_in index 190 permit ************* 22 greater-equal 22 less-equal 32
ip ip-prefix anet_ew_in index 200 permit ************* 19 greater-equal 19 less-equal 32
ip ip-prefix anet_ew_in index 210 permit ************* 19 greater-equal 19 less-equal 32
ip ip-prefix anet_ew_in index 220 permit ************ 22 greater-equal 22 less-equal 32
ip ip-prefix anet_ew_in index 230 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix anet_ew_in index 240 permit ************ 23 greater-equal 23 less-equal 32
ip ip-prefix anet_ew_in index 250 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix anet_ew_in index 260 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix anet_ew_in index 270 permit ************ 20 greater-equal 20 less-equal 32
ip ip-prefix anet_ew_in index 280 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix anet_ew_in index 290 permit ************* 24 greater-equal 32 less-equal 32
ip ip-prefix anet_ew_in index 300 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix anet_ew_in index 700 permit ************* 18 greater-equal 18 less-equal 32
ip ip-prefix anet_ew_in index 710 permit ************* 18 greater-equal 18 less-equal 32
ip ip-prefix anet_ew_out index 40 permit ************ 16 greater-equal 16 less-equal 24
ip ip-prefix anet_ew_out index 50 permit ************ 16 greater-equal 16 less-equal 24
ip ip-prefix anet_ew_out index 65 permit ************ 17 greater-equal 17 less-equal 24
ip ip-prefix anet_ew_out index 70 permit ************* 32
ip ip-prefix bnet_import_direct index 10 permit ************** 30
ip ip-prefix bnet_import_direct index 20 permit ************* 30
ip ip-prefix cu_otncpe_sdn index 20 permit *********** 8 greater-equal 8 less-equal 32
ip ip-prefix dc7 index 10 permit ************* 18 greater-equal 18 less-equal 32
ip ip-prefix dc7 index 20 permit ************* 18 greater-equal 18 less-equal 32
ip ip-prefix dc_bound index 10 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix dc_bound index 20 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix dc_bound index 30 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix dc_bound index 40 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix dc_bound index 50 permit *********** 24 greater-equal 24 less-equal 32
ip ip-prefix dc_bound_out index 10 permit ************* 26
ip ip-prefix default-refuse index 10 deny *********** 0
ip ip-prefix default-refuse index 20 permit *********** 0 less-equal 32
ip ip-prefix ha_dhtsgz index 10 permit *********** 16 greater-equal 16 less-equal 32
ip ip-prefix ha_dhtsgz index 20 permit ************* 26 greater-equal 26 less-equal 32
ip ip-prefix iotpt_zw index 20 permit ************ 16 greater-equal 16 less-equal 32
ip ip-prefix ipran_Anet index 10 permit *********** 13 greater-equal 13 less-equal 32
ip ip-prefix ipran_Anet index 20 permit ************ 13 greater-equal 13 less-equal 32
ip ip-prefix ipran_Anet index 30 permit ************ 13 greater-equal 13 less-equal 32
ip ip-prefix ipran_Anet index 40 permit ************ 13 greater-equal 13 less-equal 32
ip ip-prefix ipran_Anet index 50 permit ************ 13 greater-equal 13 less-equal 32
ip ip-prefix ipran_Anet index 60 permit ************ 16 greater-equal 16 less-equal 32
ip ip-prefix ipran_Anet index 70 permit ************ 13 greater-equal 13 less-equal 32
ip ip-prefix ipran_Anet index 80 permit ************ 14 greater-equal 14 less-equal 32
ip ip-prefix ipran_Anet index 90 permit ************ 14 greater-equal 14 less-equal 32
ip ip-prefix ipran_Anet index 100 permit ************ 13 greater-equal 13 less-equal 32
ip ip-prefix ipran_Anet index 200 permit ************* 26 greater-equal 26 less-equal 32
ip ip-prefix ipran_Anet index 210 permit ************ 13 greater-equal 13 less-equal 32
ip ip-prefix ipran_Anet index 220 permit *********** 13 greater-equal 13 less-equal 24
ip ip-prefix lousao index 10 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix lousao index 20 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix lousao index 30 permit ************** 30
ip ip-prefix lousao index 40 permit ************** 30
ip ip-prefix otn-nms index 10 permit *********** 16 greater-equal 16 less-equal 32
ip ip-prefix otn-nms index 20 permit *********** 16 greater-equal 16 less-equal 32
ip ip-prefix otn-nms index 30 permit ************ 23 greater-equal 23 less-equal 32
ip ip-prefix otn-nms index 40 permit ************ 23 greater-equal 23 less-equal 32
ip ip-prefix otn-nms index 50 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix otn-nms index 60 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix otn-nms index 70 permit 90.59.64.80 16 greater-equal 16 less-equal 32
ip ip-prefix p1_CN_MGMN_in index 10 permit 40.116.64.80 19 greater-equal 19 less-equal 24
ip ip-prefix p1_CN_MGMN_in index 20 permit 40.116.69.80 21 greater-equal 21 less-equal 24
ip ip-prefix p1_CN_MGMN_in index 30 permit 40.116.77.80 24
ip ip-prefix p1_CN_MGMN_in index 40 permit 40.116.43.80 28
ip ip-prefix p1_CN_MGMN_out index 10 permit 40.116.76.80 24
ip ip-prefix pl_5GC_MGMN index 5 permit 40.27.210.80 24
ip ip-prefix pl_5GC_MGMN index 10 permit 40.27.211.80 23
ip ip-prefix pl_5GC_MGMN index 15 permit 40.27.213.80 20
ip ip-prefix pl_5GC_MGMN index 30 permit 40.80.55.80 23
ip ip-prefix pl_5GC_MGMN index 40 permit 40.117.165.80 24
ip ip-prefix pl_5G_MEC index 10 permit 40.93.64.80 24
ip ip-prefix pl_5G_RCS index 10 permit 31.231.41.80 24
ip ip-prefix pl_CDN_MGMN-H_A index 10 permit 36.80.64.80 10 greater-equal 10 less-equal 24
ip ip-prefix pl_CU_OTNCPE_SDN index 210 permit *********** 19
ip ip-prefix pl_HW_WDM_NMS_JT_B_in index 10 permit 90.61.42.80 28 greater-equal 28 less-equal 32
ip ip-prefix pl_HW_WDM_NMS_JT_B_in index 20 permit 90.61.42.8 28 greater-equal 28 less-equal 32
ip ip-prefix pl_HW_WDM_NMS_JT_B_in index 30 permit 90.60.117.152 29 greater-equal 29 less-equal 32
ip ip-prefix pl_HW_WDM_NMS_JT_B_in index 40 permit 88.56.165.80 22 greater-equal 22 less-equal 32
ip ip-prefix pl_HW_WDM_NMS_JT_B_in index 50 permit 88.56.169.80 23 greater-equal 23 less-equal 32
ip ip-prefix pl_HW_WDM_NMS_JT_B_in index 60 permit 88.56.173.80 24 greater-equal 24 less-equal 32
ip ip-prefix pl_HW_WDM_NMS_JT_B_in index 70 permit *********** 21 greater-equal 21 less-equal 32
ip ip-prefix pl_HW_WDM_NMS_JT_B_in index 80 permit 90.61.45.80 24 greater-equal 24 less-equal 32
ip ip-prefix pl_HW_WDM_NMS_JT_B_in index 90 permit 88.58.165.80 23 greater-equal 23 less-equal 32
ip ip-prefix pl_HW_WDM_NMS_JT_B_in index 100 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix pl_HW_WDM_NMS_JT_B_in index 110 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix pl_HW_WDM_NMS_JT_B_in index 120 permit ************ 21 greater-equal 21 less-equal 32
ip ip-prefix pl_HW_WDM_NMS_JT_B_in index 130 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix pl_HW_WDM_NMS_JT_B_in index 140 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix pl_HW_WDM_NMS_JT_B_in index 150 permit ************ 22 greater-equal 22 less-equal 32
ip ip-prefix pl_HW_WDM_NMS_JT_B_in index 160 permit ************ 22 greater-equal 22 less-equal 32
ip ip-prefix pl_HW_WDM_NMS_JT_B_in index 170 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix pl_HW_WDM_NMS_JT_B_in index 180 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix pl_HW_WDM_NMS_JT_B_in index 190 permit ************ 23 greater-equal 23 less-equal 32
ip ip-prefix pl_HW_WDM_NMS_JT_B_in index 200 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix pl_HW_WDM_NMS_JT_B_in index 210 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix pl_HW_WDM_NMS_JT_B_in index 220 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix pl_HW_WDM_NMS_JT_B_in index 230 permit ************ 23 greater-equal 23 less-equal 32
ip ip-prefix pl_HW_WDM_NMS_JT_B_in index 240 permit ************ 23 greater-equal 23 less-equal 32
ip ip-prefix pl_HW_WDM_NMS_JT_B_in index 250 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix pl_HW_WDM_NMS_JT_B_in index 260 permit ************ 23 greater-equal 23 less-equal 32
ip ip-prefix pl_HW_WDM_NMS_JT_B_in index 270 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix pl_HW_WDM_NMS_JT_B_in index 280 permit *********** 23 greater-equal 23 less-equal 32
ip ip-prefix pl_HW_WDM_NMS_JT_B_in index 290 permit *********** 22 greater-equal 22 less-equal 32
ip ip-prefix pl_HW_WDM_NMS_JT_B_in index 300 permit *********** 21 greater-equal 21 less-equal 32
ip ip-prefix pl_HW_WDM_NMS_JT_B_in index 310 permit *********** 24 greater-equal 24 less-equal 32
ip ip-prefix pl_HW_WDM_NMS_JT_B_in index 320 permit *********** 24 greater-equal 24 less-equal 32
ip ip-prefix pl_IMS_VPN index 10 permit ************* 24
ip ip-prefix pl_IMS_VPN index 20 permit ************* 24
ip ip-prefix pl_IOTPT_ZW index 10 permit ************ 23
ip ip-prefix pl_IOT_BSS index 10 permit ************ 24
ip ip-prefix pl_IPRAN_JT index 10 permit ************** 24
ip ip-prefix pl_IPRAN_NMS_JT index 10 permit ************ 16 greater-equal 16 less-equal 32
ip ip-prefix pl_IoT_OSS index 10 permit *********** 27
ip ip-prefix pl_IoT_OSS index 20 permit ************ 28
ip ip-prefix pl_MVSP_NMS index 10 permit ************ 24
ip ip-prefix pl_MVSP_NMS index 20 permit ************ 24
ip ip-prefix pl_MVSP_VS index 10 permit ************ 24
ip ip-prefix pl_MVSP_VS index 20 permit ************ 24
ip ip-prefix pl_MVSP_VS index 30 permit ************ 24
ip ip-prefix pl_NKFVPN index 10 permit *********** 24 greater-equal 24 less-equal 32
ip ip-prefix pl_OSS_JT index 10 permit ************ 12 greater-equal 12 less-equal 32
ip ip-prefix pl_OSS_JT index 20 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix pl_OSS_JT index 30 permit ************ 16 greater-equal 16 less-equal 32
ip ip-prefix pl_OSS_JT index 40 permit ************ 16 greater-equal 16 less-equal 32
ip ip-prefix pl_OSS_JT index 50 permit ************* 22 greater-equal 22 less-equal 32
ip ip-prefix pl_OSS_JT index 60 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix pl_OSS_JT index 70 permit ************ 24
ip ip-prefix pl_OSS_JT index 80 permit ************ 23
ip ip-prefix pl_OSS_JT index 90 permit ************ 16 greater-equal 16 less-equal 24
ip ip-prefix pl_OSS_JT index 105 permit ************ 17 greater-equal 17 less-equal 24
ip ip-prefix pl_OSS_JT index 110 permit ************ 16 greater-equal 16 less-equal 24
ip ip-prefix pl_OSS_JT index 120 permit ************* 26
ip ip-prefix pl_OSS_JT index 130 permit ************* 26
ip ip-prefix pl_OSS_JT index 140 permit ************* 25
ip ip-prefix pl_OSS_JT index 150 permit ************* 25
ip ip-prefix pl_OTN_NMS_JT index 10 permit ************ 25
ip ip-prefix pl_OTN_NMS_JT index 20 permit ************* 26
ip ip-prefix pl_OTN_NMS_JT index 30 permit ************* 27
ip ip-prefix pl_OTN_NMS_JT index 40 permit ************ 24
ip ip-prefix pl_OTN_NMS_JT index 50 permit ************ 24
ip ip-prefix pl_SDOTN_CONTR index 10 permit ************* 25
ip ip-prefix pl_SDOTN_CONTR index 20 permit ************ 24
ip ip-prefix pl_SDOTN_CONTR index 30 permit ************ 22 greater-equal 22 less-equal 24
ip ip-prefix pl_SDOTN_CONTR index 40 permit ************ 20 greater-equal 20 less-equal 24
ip ip-prefix pl_SDOTN_CONTR index 50 permit ************* 18 greater-equal 18 less-equal 24
ip ip-prefix pl_SDOTN_CONTR index 60 permit ************* 20 greater-equal 20 less-equal 32
ip ip-prefix pl_SDOTN_JT index 10 permit ************ 26
ip ip-prefix pl_SDOTN_JT index 15 permit ************ 24
ip ip-prefix pl_SDOTN_JT index 20 permit ************ 24
ip ip-prefix pl_SDOTN_JT index 30 permit ************* 26
ip ip-prefix pl_SDOTN_JT index 40 permit ************ 24
ip ip-prefix pl_SDOTN_JT index 50 permit ************ 24
ip ip-prefix pl_SDOTN_JT index 60 permit ************ 24
ip ip-prefix pl_SDOTN_JT index 70 permit ************ 24
ip ip-prefix pl_SDOTN_JT index 80 permit ************ 24
ip ip-prefix pl_SDOTN_JT index 90 permit ************ 24
ip ip-prefix pl_SDOTN_JT index 100 permit ************ 24
ip ip-prefix pl_SDOTN_JT index 110 permit ************* 25
ip ip-prefix pl_SDOTN_JT index 120 permit ************ 24
ip ip-prefix pl_SDOTN_JT index 130 permit ************ 24
ip ip-prefix pl_SDOTN_JT index 140 permit ************ 24
ip ip-prefix pl_SDOTN_JT index 150 permit ************ 24
ip ip-prefix pl_SDOTN_JT index 160 permit ************ 24
ip ip-prefix pl_SDOTN_JT index 170 permit ************ 24
ip ip-prefix pl_SDOTN_JT index 180 permit ************ 24
ip ip-prefix pl_SDOTN_JT index 190 permit ************ 24
ip ip-prefix pl_SDOTN_JT index 200 permit ************ 24
ip ip-prefix pl_SDOTN_JT index 210 permit ************ 24
ip ip-prefix pl_UTC_MGMN index 10 permit *********** 24
ip ip-prefix pl_UTC_MGMN index 20 permit *********** 24
ip ip-prefix pl_UTC_MGMN index 30 permit *********** 24
ip ip-prefix sdotn_cq index 10 permit *********** 23 greater-equal 23 less-equal 32
ip ip-prefix sdotn_cq index 20 permit ************* 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_gs_hw index 10 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_gs_hw index 20 permit ************ 25 greater-equal 25 less-equal 32
ip ip-prefix sdotn_gs_hw index 30 permit ************ 21 greater-equal 21 less-equal 32
ip ip-prefix sdotn_gs_hw index 40 permit ************* 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_gs_hw index 50 permit ************* 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_gs_hw index 60 permit ************* 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_gs_hw index 70 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_jt index 10 permit ************* 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_jt index 20 permit ************** 25 greater-equal 25 less-equal 32
ip ip-prefix sdotn_jt index 30 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_jt index 40 permit 91.80.166.80 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_jt index 50 permit 91.80.59.80 25 greater-equal 25 less-equal 32
ip ip-prefix sdotn_jt index 60 permit 92.184.126.80 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_jt index 70 permit 90.61.142.80 24
ip ip-prefix sdotn_jt index 80 permit 92.57.193.80 24
ip ip-prefix sdotn_jt index 90 permit 90.249.201.80 24
ip ip-prefix sdotn_jt index 100 permit 90.152.139.80 24
ip ip-prefix sdotn_jt index 110 permit 92.175.64.80 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_jt index 120 permit 91.80.53.80 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_jt index 130 permit 91.88.69.80 28
ip ip-prefix sdotn_jt index 140 permit 90.216.54.80 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_jt index 150 permit 91.248.136.80 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_jt index 160 permit 92.122.169.80 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_jt index 170 permit 93.192.65.80 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_jt index 180 permit 90.61.136.80 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_jt index 190 permit 90.61.146.80 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_jt index 200 permit ************* 24
ip ip-prefix sdotn_jt index 210 permit ************* 24
ip ip-prefix sdotn_jt index 220 permit 91.184.188.80 24
ip ip-prefix sdotn_jt index 230 permit 92.247.236.80 24
ip ip-prefix sdotn_jt index 240 permit 92.184.126.80 24
ip ip-prefix sdotn_jt index 250 permit 91.217.57.184 28
ip ip-prefix sdotn_jt index 260 permit 92.159.159.80 24
ip ip-prefix sdotn_jt index 270 permit *********** 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_jt index 280 permit 90.61.152.80 24
ip ip-prefix sdotn_jt index 290 permit 93.179.64.80 24
ip ip-prefix sdotn_jt index 300 permit 90.62.39.80 24
ip ip-prefix sdotn_jt index 310 permit 93.80.55.80 24
ip ip-prefix sdotn_jt index 320 permit 90.61.134.100 30
ip ip-prefix sdotn_jt index 330 permit 90.61.135.80 24
ip ip-prefix sdotn_jt index 340 permit 90.61.137.80 24
ip ip-prefix sdotn_jt index 350 permit 90.61.138.80 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_jt index 360 permit 90.61.139.80 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_jt index 370 permit 90.61.140.80 24
ip ip-prefix sdotn_jt index 380 permit 90.152.140.80 24
ip ip-prefix sdotn_jt index 390 permit 90.61.143.80 24
ip ip-prefix sdotn_jt index 400 permit 90.61.144.80 24
ip ip-prefix sdotn_jt index 410 permit 90.61.146.180 32
ip ip-prefix sdotn_jt index 420 permit 90.61.146.181 32
ip ip-prefix sdotn_jt index 430 permit 90.61.178.80 24
ip ip-prefix sdotn_jt index 440 permit 90.61.148.80 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_jt index 450 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_jt index 490 permit ************ 24
ip ip-prefix sdotn_jt index 500 permit ************ 24
ip ip-prefix sdotn_jt index 510 permit ************ 24
ip ip-prefix sdotn_jt index 520 permit ************ 24
ip ip-prefix sdotn_jt index 530 permit ************ 24
ip ip-prefix sdotn_jt index 540 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_jt index 550 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_jt index 560 permit *********** 24
ip ip-prefix sdotn_jt index 570 permit *********** 24
ip ip-prefix sdotn_jt index 580 permit ************* 24
ip ip-prefix sdotn_jt index 590 permit ************ 24
ip ip-prefix sdotn_jt index 600 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_jt index 610 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_jt index 620 permit ************ 28
ip ip-prefix sdotn_jt index 630 permit *********** 25
ip ip-prefix sdotn_jt index 640 permit ************* 26
ip ip-prefix sdotn_jt index 650 permit *********** 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_jt index 660 permit ************* 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_jt index 670 permit *********** 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_jt index 680 permit ************ 25
ip ip-prefix sdotn_jt index 690 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_jt index 700 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_jt index 710 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_jt index 730 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_jt index 740 permit ************* 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_jt index 750 permit *********** 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_jt index 760 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_jt index 770 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_jt index 780 permit ************* 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_jt index 790 permit ************* 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_jt index 800 permit ************* 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_jt index 810 permit ************* 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_jt index 820 permit ************* 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_jt index 830 permit ************* 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_jt index 840 permit ************* 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_jt index 850 permit ************ 24
ip ip-prefix sdotn_jt index 860 permit ************* 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_jt index 870 permit ************* 32
ip ip-prefix sdotn_jt index 880 permit *********** 24
ip ip-prefix sdotn_ln_zx index 10 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_ln_zx index 20 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_ln_zx index 30 permit ************* 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_ln_zx index 40 permit ************* 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_ln_zx index 50 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_ln_zx index 60 permit ************* 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_ln_zx index 70 permit ************* 23 greater-equal 23 less-equal 32
ip ip-prefix sdotn_ln_zx index 80 permit ************* 23 greater-equal 23 less-equal 32
ip ip-prefix sdotn_ln_zx index 90 permit ************* 23 greater-equal 23 less-equal 32
ip ip-prefix sdotn_ln_zx index 100 permit ************* 23 greater-equal 23 less-equal 32
ip ip-prefix sdotn_ln_zx index 110 permit ************* 23 greater-equal 23 less-equal 32
ip ip-prefix sdotn_ln_zx index 120 permit ************* 23 greater-equal 23 less-equal 32
ip ip-prefix sdotn_ln_zx index 130 permit ************* 23 greater-equal 23 less-equal 32
ip ip-prefix sdotn_ln_zx index 140 permit ************* 23 greater-equal 23 less-equal 32
ip ip-prefix sdotn_ln_zx index 150 permit ************* 23 greater-equal 23 less-equal 32
ip ip-prefix sdotn_ln_zx index 160 permit ************* 23 greater-equal 23 less-equal 32
ip ip-prefix sdotn_ln_zx index 170 permit ************* 23 greater-equal 23 less-equal 32
ip ip-prefix sdotn_ln_zx index 180 permit ************* 23 greater-equal 23 less-equal 32
ip ip-prefix sdotn_ln_zx index 190 permit ************* 23 greater-equal 23 less-equal 32
ip ip-prefix sdotn_ln_zx index 200 permit ************* 23 greater-equal 23 less-equal 32
ip ip-prefix sdotn_sc index 10 permit *********** 24
ip ip-prefix sdotn_sc index 20 permit *********** 24
ip ip-prefix sdotn_sc index 30 permit *********** 24
ip ip-prefix sdotn_sc index 40 permit *********** 24
ip ip-prefix sdotn_sc index 50 permit *********** 24
ip ip-prefix sdotn_sc index 60 permit *********** 24
ip ip-prefix sdotn_sc index 70 permit *********** 24
ip ip-prefix sdotn_sc index 80 permit ************* 30
ip ip-prefix sdotn_sc index 90 permit ************* 30
ip ip-prefix sdotn_sc index 100 permit ************ 16 greater-equal 16 less-equal 24
ip ip-prefix sdotn_sc index 110 permit *********** 24
ip ip-prefix sdotn_sc index 120 permit *********** 24
ip ip-prefix sdotn_sc index 130 permit *********** 24
ip ip-prefix sdotn_yn_hw index 10 permit *********** 16 greater-equal 16 less-equal 32
ip ip-prefix sdotn_yn_hw index 20 permit ************ 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_yn_hw index 30 permit *********** 24 greater-equal 24 less-equal 32
ip ip-prefix sdotn_yn_hw index 40 permit *********** 24 greater-equal 24 less-equal 32
ip ip-prefix untrust_VR_MGMT index 10 permit ************* 24
ip ip-prefix untrust_VR_MGMT index 20 permit ************* 23
ip ip-prefix untrust_VR_MGMT index 30 permit ********** 16 greater-equal 16 less-equal 24
ip ip-prefix untrust_VR_MGMT index 40 permit ************* 23
ip ip-prefix untrust_VR_MGMT index 50 permit ************* 24
ip ip-prefix untrust_VR_MGMT index 60 permit ************* 24
ip ip-prefix znzx index 10 permit *********** 8 greater-equal 8 less-equal 32
ip ip-prefix znzx index 20 permit *********** 8 greater-equal 8 less-equal 32
ip ip-prefix znzx index 30 permit *********** 24 greater-equal 24 less-equal 32
ip ip-prefix znzx index 40 permit *********** 24 greater-equal 24 less-equal 32
ip ip-prefix znzx_nms index 10 permit *********** 8 greater-equal 8 less-equal 32
ip ip-prefix znzx_nms index 20 permit *********** 24 greater-equal 24 less-equal 32
#
ip route-static vpn-instance mgt *********** *********** ************* description MGMT
ip route-static vpn-instance BSS-SN_A ************* *************** ************** description TO-SanXi-BSS_2002935085
ip route-static vpn-instance BSS-SN_A ************ *************** ************** description 2002943109
ip route-static vpn-instance BSS-SN_A ************ *************** ************** description TO-JILIN-BSS
ip route-static vpn-instance BSS-SN_A ************ *************** ************** description TO-JILIN-BSS
ip route-static vpn-instance BSS-SN_A ************* *************** ************** description TO-YUNNAN-BSS
ip route-static vpn-instance BSS-SN_A ************* *************** ************** description TO-YUNNAN-BSS
ip route-static vpn-instance OSS-SN_A ************ *************** ************** description TO-ShanXi-taiyuan-OSS
ip route-static vpn-instance OSS-SN_A ************* *************** ************** description CROP-20210924-0-13510
ip route-static vpn-instance OSS-SN_A ************** *************** ************** description CROP-20211028-0-14853_to_OSS
ip route-static vpn-instance OSS-SN_A ************* 255.255.185.80 ************** description TO-HeNan-OSS
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS-20211018-0-14375
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS-20211018-0-14375
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS-20211018-0-14375
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS-20211018-0-14375
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS-20211018-0-14375
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS-20211018-0-14375
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS-20211018-0-14375
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS-20211018-0-14375
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS-20211018-0-14375
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS-20211018-0-14375
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS-20211018-0-14375
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS-20211018-0-14375
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS-20211018-0-14375
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS-20211018-0-14375
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS-20211018-0-14375
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS-20211018-0-14375
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS-20211018-0-14375
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS-20211018-0-14375
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS-20211018-0-14375
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS-20211018-0-14375
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS-20211018-0-14375
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS-20211018-0-14375
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS-20211018-0-14375
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS-20211018-0-14375
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS-20211018-0-14375
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS-20211018-0-14375
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS-20211018-0-14375
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS-20211018-0-14375
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS-20211018-0-14375
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS-20211018-0-14375
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS-20211018-0-14375
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS-20211018-0-14375
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS-20211018-0-14375
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS-20211018-0-14375
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS-20211018-0-14375
ip route-static vpn-instance OSS-SN_A ************* ************** ************** description TO-HeNan-OSS-20211018-0-14375
ip route-static vpn-instance OSS-SN_A ************ *************** ************** description TO-shanghai-OSS
ip route-static vpn-instance OSS-SN_A ************** *************** ************** description TO-OSS-CROP-20210831-0-12323
ip route-static vpn-instance OSS-SN_A ************** *************** ************** description TO-FuJian-OSS
ip route-static vpn-instance OSS-SN_A *********** *************** ************** description TO-gansu-OSS
ip route-static vpn-instance OSS-SN_A *********** *************** ************** description TO-gansu-OSS
ip route-static vpn-instance OSS-SN_A *********** *************** ************** description TO-gansu-OSS
ip route-static vpn-instance OSS-SN_A *********** *************** ************** description TO-gansu-OSS
ip route-static vpn-instance OSS-SN_A *********** *************** ************** description TO-gansu-OSS
ip route-static vpn-instance OSS-SN_A *********** *************** ************** description TO-gansu-OSS
ip route-static vpn-instance OSS-SN_A ************ *************** ************** description CROP-20210315-0-05244_TO_OSS
ip route-static vpn-instance vrf_169 *********** *********** 150.192.95.11 description to-internet
ip route-static vpn-instance vrf_406 130.48.180.80 ************** 130.40.255.161
#
ipv6 route-static vpn-instance vrf_169 :: 0 2408:8001:680:8000::BD2 description to-ipv6-internet
#
ip ipv6-prefix 5gc_mgmn index 10 permit 2408:81B0:A00:1:: 64
ip ipv6-prefix 5gc_mgmn index 100 permit 2408:81A0:8000:4000:: 60
ip ipv6-prefix 5gc_mgmn index 110 permit 2408:8142:60FF:FB0C:: 64
ip ipv6-prefix 5gc_mgmn index 115 permit 2408:8142:60FF:FB0B:: 64
ip ipv6-prefix 5gc_mgmn index 125 permit 2408:81A0:8000:6000:: 60
ip ipv6-prefix 5gc_mgmn index 135 permit 2408:81A0:C000:8000:: 52
ip ipv6-prefix 5gc_mgmn index 145 permit 2408:81A0:2000:4000:: 64
ip ipv6-prefix 5gc_mgmn index 155 permit 2408:81A0:8000:8000:: 60
ip ipv6-prefix 5gc_mgmn index 165 permit 2408:81A0:8000:A000:: 60
ip ipv6-prefix 5gc_mgmn index 175 permit 2408:8142:E0FF:FB11:: 64
ip ipv6-prefix 5gc_mgmn index 185 permit 2408:81A0:C000:A000:: 52
ip ipv6-prefix 5gc_mgmn index 195 permit 2408:81A0:C000:600:: 60
ip ipv6-prefix 5gc_mgmn index 205 permit 2408:81A0:C000:6000:: 52
ip ipv6-prefix 5gc_mgmn index 215 permit 2408:81A0:8000:100:: 60
ip ipv6-prefix 5gc_mgmn index 225 permit 2408:8142:E0FF:FB02:: 64
ip ipv6-prefix 5gc_mgmn index 235 permit 2408:81A0:C000:C000:: 52
ip ipv6-prefix 5gc_mgmn index 245 permit 2408:81A3:60EE:FB80:: 64
ip ipv6-prefix 5gc_mgmn index 255 permit 2408:81A3:60EE:FB90:: 64
ip ipv6-prefix 5gc_mgmn index 265 permit 2408:81A0:2000:6000:: 64
ip ipv6-prefix 5gc_mgmn index 275 permit 2408:8141:80FF:FB02:: 64
ip ipv6-prefix 5gc_mgmn index 285 permit 2408:8141:80FF:FB03:: 64
ip ipv6-prefix 5gc_mgmn index 295 permit 2408:81A1:8002:FFF2:: 64
ip ipv6-prefix 5gc_mgmn index 305 permit 2408:8140:3FFF:FB00:: 64
ip ipv6-prefix 5gc_mgmn index 315 permit 2408:8140:3FFF:FB01:: 64
ip ipv6-prefix 5gc_mgmn index 325 permit 2408:81A2:C000:100:: 64
ip ipv6-prefix 5gc_mgmn index 335 permit 2408:81A2:C000:1:: 80
ip ipv6-prefix 5gc_mgmn index 345 permit 2408:81A2:C000:110:: 64
ip ipv6-prefix 5gc_mgmn index 355 permit 2408:81A2:C000:: 80
ip ipv6-prefix 5gc_mgmn index 365 permit 2408:8141:80FF:FB00:: 64
ip ipv6-prefix 5gc_mgmn index 375 permit 2408:8143:E0F1:100:: 56
ip ipv6-prefix 5gc_mgmn index 385 permit 2408:81A0:2000:1:: 64
ip ipv6-prefix 5gc_mgmn index 395 permit 2408:8140:C0FF:FB00:: 60
ip ipv6-prefix 5gc_mgmn index 405 permit 2408:8140:40F0:FB00:: 64
ip ipv6-prefix 5gc_mgmn index 415 permit 2408:8141:80FF:FB00::60 123
ip ipv6-prefix 5gc_mgmn index 425 permit 2408:8140:40F0:FB00:101:F006:: 96
ip ipv6-prefix 5gc_mgmn index 435 permit 2408:8140:40F0:FB00:101:FE06:: 96
ip ipv6-prefix 5gc_nef index 10 permit 2408:8140:C0FF:F9F0:: 60 greater-equal 60 less-equal 128
ip ipv6-prefix 5gc_nef index 20 permit 2408:8142:60FF:F9F0:: 60 greater-equal 60 less-equal 128
ip ipv6-prefix 5gc_nef index 30 permit 2408:8140:C0FF:F900:: 60 greater-equal 60 less-equal 128
ip ipv6-prefix 5gc_nef index 40 permit 2408:8142:60FF:F900:: 60 greater-equal 60 less-equal 128
ip ipv6-prefix 5gc_rcs_in index 10 permit 2408:8141:8003:100:B:61:2:0 112
ip ipv6-prefix 5gc_rcs_in index 20 permit 2408:8141:8003:102:B:61:2:0 112
ip ipv6-prefix 5gc_rcs_in index 30 permit 2408:8142:6003:100::8FA 128
ip ipv6-prefix 5gc_rcs_in index 40 permit 2408:8142:6003:101::AFA 128
ip ipv6-prefix 5gc_rcs_in index 50 permit 2408:8140:4003:100::14:0 112
ip ipv6-prefix 5gc_rcs_in index 60 permit 2408:8140:4003:100::2A:0 112
ip ipv6-prefix 5gc_rcs_in index 70 permit 2408:8140:2003:100::1:0 112
ip ipv6-prefix 5gc_rcs_in index 80 permit 2408:8140:2003:101::1:0 112
ip ipv6-prefix 5gc_rcs_in index 90 permit 2408:8142:6003:100::800 120
ip ipv6-prefix 5gc_rcs_in index 100 permit 2408:8142:6003:101::A00 120
ip ipv6-prefix 5gc_rcs_in index 110 permit 2408:8141:8003:102:: 64
ip ipv6-prefix 5gc_rcs_in index 120 permit 2408:8141:8003:100:: 64
ip ipv6-prefix 5gc_rcs_in index 130 permit 2408:8140:2003:100:: 64
ip ipv6-prefix 5gc_rcs_in index 140 permit 2408:8140:2003:101:: 64
ip ipv6-prefix 5gc_rcs_in index 150 permit 2408:8142:6003:100:: 64
ip ipv6-prefix 5gc_rcs_in index 160 permit 2408:8142:6003:101:: 64
ip ipv6-prefix 5gc_rcs_out index 10 permit 2408:81B0:A00:6:: 64
ip ipv6-prefix 5gc_test_in index 10 permit 2408:8140:F0:1000:: 52 greater-equal 52 less-equal 64
ip ipv6-prefix 5gc_test_in index 20 permit 2408:8140:F0:1000:: 60 greater-equal 60 less-equal 64
ip ipv6-prefix 5gc_test_in index 30 permit 2408:8140:F0:2002:: 64
ip ipv6-prefix 5gc_test_in index 40 permit 2408:8140:F0:: 52 greater-equal 52 less-equal 64
ip ipv6-prefix 5gc_test_in index 50 permit 2408:8140:F0:2000:1:5:: 96
ip ipv6-prefix 5gc_test_in index 60 permit 2408:8140:F0:2000:: 64
ip ipv6-prefix 5gc_test_out index 10 permit 2408:81B0:A00:1:: 64
ip ipv6-prefix ipv6-default-refuse index 10 deny :: 0
ip ipv6-prefix ipv6-default-refuse index 20 permit :: 0 less-equal 128
ip ipv6-prefix mvsp_vs_in index 10 permit 2408:8141:8003:: 64
ip ipv6-prefix mvsp_vs_in index 20 permit 2408:8141:8003:4:: 64
ip ipv6-prefix mvsp_vs_in index 30 permit 2408:8141:8003:0:A:10:1:0 112
ip ipv6-prefix mvsp_vs_in index 40 permit 2408:8141:8003:4:A:10:1:0 112
ip ipv6-prefix mvsp_vs_in index 50 permit 2408:8142:6003:1:: 64
ip ipv6-prefix mvsp_vs_in index 60 permit 2408:8142:6003:: 64
ip ipv6-prefix mvsp_vs_in index 70 permit 2408:8142:6003:1:: 120
ip ipv6-prefix mvsp_vs_in index 80 permit 2408:8142:6003:: 120
ip ipv6-prefix utc_mgmn index 10 permit 2408:81B0:A00:1:: 64
ip ipv6-prefix utc_mgmn index 100 permit 2408:81A2:E000:2000:: 64
ip ipv6-prefix utc_mgmn index 105 permit 2408:81A2:E000:1:: 64
ip ipv6-prefix utc_mgmn index 110 permit 2408:81A0:C000:2000:: 52 greater-equal 52 less-equal 64
ip ipv6-prefix utc_mgmn index 115 permit 2408:81A0:C000:4000:: 52 greater-equal 52 less-equal 64
ip ipv6-prefix utc_mgmn index 125 permit 2408:81A0:2000:2100:: 64
ip ipv6-prefix utc_mgmn index 135 permit 2408:81A0:40FF:DC00:: 56
ip ipv6-prefix utc_mgmn index 145 permit 2408:81A0:40FF:FB00:: 56
ip ipv6-prefix utc_mgmn index 155 permit 2408:81A3:60FF:FB80:: 64
ip ipv6-prefix utc_mgmn index 165 permit 2408:81A3:60FF:FB90:: 64
ip ipv6-prefix utc_mgmn index 175 permit 2408:81A1:8022:FF04:: 64
ip ipv6-prefix utc_mgmn index 185 permit 2408:81A2:E000:2120:: 64
ip ipv6-prefix utc_mgmn index 195 permit 2408:8142:6003:721:: 64
ip ipv6-prefix utc_mgmn index 205 permit 2408:81A1:8022:FF00:: 64
ip ipv6-prefix utc_mgmn index 215 permit 2408:81A1:8023:FF00:: 64
ip ipv6-prefix utc_mgmn index 225 permit 2408:8142:6003:700:: 64
#
snmp-agent
snmp-agent local-engineid 800007DB032C52AFDAC812
snmp-agent community read cipher %^%#nO,02ih6/%FgKoBy<%`!xbmhW0dHV(|q<(I2X3y/>CdYPDZ2IQY_>YRE03|H5bMb%ZtCm)irCMJ{U)G,%^%# acl 2222 alias __CommunityAliasName_01_59498
#
snmp-agent sys-info version all
snmp-agent target-host host-name __targetHost_10_58385 trap address udp-domain ************ vpn-instance mgt params securityname cipher %^%#\@'fCx]9s$2/,y9.MT!7bN:R!B.xtFnfZ!J80C"D%^%#
snmp-agent target-host host-name __targetHost_11_16218 trap address udp-domain ************* vpn-instance mgt params securityname cipher %^%#M^9WW|2B3"!GB09'zhtQ2%tQ-0f9l:&!m7@8/]l.%^%#
snmp-agent target-host host-name __targetHost_1_33122 trap address udp-domain ************ vpn-instance mgt params securityname cipher %^%#,3{d6QsSL4=oYI5*OMfAY>}10@_7V)zI-TD',@m>%^%#
snmp-agent target-host host-name __targetHost_2_57941 trap address udp-domain ************ vpn-instance mgt params securityname cipher %^%#:u@VWKJ:n1'd6cS{JCN*a8jv<V(<Q-(CAR505k95%^%#
snmp-agent target-host host-name __targetHost_3_8470 trap address udp-domain ************ vpn-instance mgt params securityname cipher %^%#jji)38i4L;ozG_:u3M(9vATC%'2jZ/D;ZA8HsgF#%^%#
snmp-agent target-host host-name __targetHost_4_56324 trap address udp-domain ************ vpn-instance mgt params securityname cipher %^%#SY^C/vw3h/{.uiCGict,r^34,TSpu:Cmf0Mm|wF<%^%#
#
snmp-agent trap source GigabitEthernet0/0/0
#
snmp-agent protocol source-status all-interface
undo snmp-agent protocol source-status ipv6 all-interface
#
undo snmp-agent proxy protocol source-status all-interface
undo snmp-agent proxy protocol source-status ipv6 all-interface
#
snmp-agent trap enable
#
lldp enable
#
undo web-auth-server source-ip all
#
undo web-auth-server source-ipv6 all
#
stelnet server enable

ssh server rsa-key min-length 3072
ssh server-source all-interface
ssh ipv6 server-source all-interface
ssh authorization-type default aaa
#
ssh server cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr
ssh server hmac sha2_512 sha2_256
ssh server key-exchange dh_group_exchange_sha256 dh_group_exchange_sha1 dh_group14_sha1 ecdh_sha2_nistp256 ecdh_sha2_nistp384 ecdh_sha2_nistp521
#
ssh server publickey ecc rsa rsa_sha2_256 rsa_sha2_512
#
ssh server dh-exchange min-len 3072
#
ssh client first-time enable
#
ssh client publickey ecc rsa rsa_sha2_256 rsa_sha2_512
#
ssh client cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr
ssh client hmac sha2_512 sha2_256
ssh client key-exchange dh_group_exchange_sha256 dh_group_exchange_sha1 dh_group14_sha1 ecdh_sha2_nistp256 ecdh_sha2_nistp384 ecdh_sha2_nistp521
#
command-privilege level 1 view global display current-configuration
command-privilege level 1 view shell screen-length
#
user-interface maximum-vty 9
#
user-interface con 0
 authentication-mode password
 
#
user-interface aux 0
 undo shell
#
user-interface vty 0 8
 authentication-mode aaa
 protocol inbound ssh
#
netconf
 activate module huawei-ip
#
local-aaa-server
#
pki domain default
#

#

#
warranty
#
return
<HAZZ-DC1-3F1-R08C17U03-ZGY-OSS-CE-RT01-HWNE40E>
<HAZZ-DC1-3F1-R08C17U03-ZGY-OSS-CE-RT01-HWNE40E>quit

