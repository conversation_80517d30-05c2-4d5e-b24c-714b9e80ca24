#
//这是第三个VXLAN桥接域接口配置，创建Vbdif1149接口，将其绑定到VPC_wlcpyfs_DB_50262 VPN实例，配置IP地址为**************/24，MAC地址为0000-5e00-0102，启用VXLAN任播网关和ARP主机信息收集功能。该接口作为数据库VPC的VXLAN网络三层网关。//
interface Vbdif1149
 ip binding vpn-instance VPC_wlcpyfs_DB_50262
 ip address ************** ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable

//这是管理以太网接口配置，配置MEth0/0/0接口，将其绑定到mgmt VPN实例，设置IP地址为************/18。该接口用于设备的带外管理，与数据平面隔离，提供独立的管理通道。//
interface MEth0/0/0
 ip binding vpn-instance mgmt
 ip address ************ **************
