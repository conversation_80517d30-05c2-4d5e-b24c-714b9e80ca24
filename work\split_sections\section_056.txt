#
//这是开放最短路径优先(OSPF)协议配置，创建OSPF进程10，设置路由器ID为130.40.39.47，配置SPF计算间隔、LSA生成间隔和LSA到达间隔均使用智能定时器模式（初始值、增量值和最大值均为50毫秒），创建OSPF区域75.80.64.80，并在该区域内通告多个网段，包括环回接口地址和物理接口地址。OSPF用于网络内部的路由信息交换，通过最短路径优先算法计算最佳路由，提供快速的路由收敛。//
ospf 10 router-id 130.40.39.47
 spf-schedule-interval intelligent-timer 50 50 50
 lsa-originate-interval intelligent-timer 50 50 50
 lsa-arrival-interval intelligent-timer 50 50 50
 area 75.80.64.80
  network 130.40.39.47 75.80.64.80
  network 130.40.40.10 75.80.64.80
  network 130.40.45.8 75.80.64.21
  network 130.40.45.12 75.80.64.21
  network 130.40.45.16 75.80.64.21
  network 130.40.45.20 75.80.64.21
