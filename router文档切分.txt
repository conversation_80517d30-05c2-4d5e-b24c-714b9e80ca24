参考链接：http://support.huawei.com/enterprise/zh/doc/EDOC1100457368/22f7fd5a

安全性：ip转换
   大模型生成网络拓扑（验证整篇、分段）、deepseek R1、qwen2.5 72B VL、qwen3-32B 记录提问方式和具体模型返回
   配置命令解析（配置助手验证）


-- 日志配置
配置：
info-center source cli channel 2 log level notification
info-center source ssh channel 2 trap level informational
info-center source default channel 2 log level warning trap level warning
info-center source ssh channel 4 log level informational
info-center loghost source GigabitEthernet0/0/0
info-center loghost ************ vpn-instance 
作用：
日志配置，配置日志信息
参数说明：
cli：显示指定命令行用户的日志信息。
info-center loghost source：命令用来配置设备向日志主机发送消息的源接口信息。

-- ntp时钟
配置：
ntp-service server disable
ntp-service ipv6 server disable
ntp-service server source-interface all disable
ntp-service ipv6 server source-interface all disable
ntp-service unicast-server ********** vpn-instance mgt
ntp-service authentication enable
作用：
命令用来配置最大系统轮询时间、时钟服务器与客户端之间的时间差和客户端时钟同步的最大时间间隔。
参数说明：
ntp-service server disable：命令用来去使能NTP服务器功能。
ntp-service unicast-server：命令用来配置NTP服务器模式。
ntp-service authentication enable：命令用来设置NTP身份认证功能。

-- 配置VPN实例

配置：
ip vpn-instance 5G-TF_A
 ipv4-family
  route-distinguisher 65159:114
  apply-label per-instance
  vpn-target 65159:114 export-extcommunity
  vpn-target 65159:114 import-extcommunity
 ipv6-family
  route-distinguisher 65159:114
  apply-label per-instance
  vpn-target 65159:114 export-extcommunity
  vpn-target 65159:114 import-extcommunity
作用：
配置VPN实例，指定VPN实例的信息
参数说明：
ip vpn-instance [ vpn-instance-name ]中的vpn-instance-name：显示指定VPN实例的信息
route-distinguisher：命令用来为EVPN实例配置RD
vpn-target：命令用来配置MPLS-L2VPN实例的VPN-Target属性。
import-extcommunity：
指定入方向路由信息携带的VPN-Target扩展团体属性值。
export-extcommunity：
指定出方向路由信息携带的VPN-Target扩展团体属性值
apply-label per-instance：命令用来配置当前VPN实例地址族下所有发往对端PE的路由都使用同一个标签值。

-- 骨干网用vpn
配置：
mpls lsr-id *************
作用：
mpls lsr-id：命令用来配置LSR的ID。骨干网用vpn

-- acl number 对接限制，接口调用，策略，生成策略后会调用
配置：
acl number 2222
 description SNMP
 rule 5 permit vpn-instance mgt source *********** ********
 rule 10 permit vpn-instance mgt source ************* 0
 rule 15 permit vpn-instance mgt source ************ 0
#
acl number 3000
 rule 5 deny tcp destination-port eq 9995
 rule 10 deny tcp destination-port eq 445
 rule 15 deny udp destination-port eq 445
 rule 20 deny udp destination-port eq netbios-ns
 rule 25 deny udp destination-port eq netbios-dgm
 rule 30 deny tcp destination-port eq 9996
 rule 35 deny udp destination-port eq tftp
 rule 40 deny tcp destination-port eq 4444
 rule 45 deny tcp destination-port eq 593
 rule 50 deny udp destination-port eq 593
 rule 55 deny udp destination-port eq 1434
 rule 60 deny tcp destination-port eq 5554
 rule 65 deny tcp destination-port eq 135
 rule 70 deny udp destination-port eq netbios-ssn
 rule 75 deny tcp destination-port eq 139
 rule 80 deny udp destination-port eq 135
#
acl number 3022
 rule 10 permit ip vpn-instance mgt source ************* *********
 rule 15 permit ip vpn-instance mgt source ********* *************
 rule 20 permit ip vpn-instance mgt source ********** ***********
 rule 25 permit ip vpn-instance mgt source ************ 0
 rule 30 permit ip vpn-instance mgt source ********** *********
 rule 35 permit ip vpn-instance mgt source ********** *********
 rule 40 permit ip vpn-instance mgt source ********** *********
 rule 45 permit ip vpn-instance mgt source *********** ********
作用：
对接限制，接口调用，策略，生成策略后会调用
参数说明：
acl number：命令用来创建一个ACL，并进入ACL视图。如果用户需要创建的ACL已经存在时，执行该命令将直接进入该ACL视图。

待补充


-- interface Eth-Truck 聚合口配置
配置：
interface Eth-Trunk0
 mtu 9600
 description To-[HAZZ-DC1-3F2-R04C17U03-ZGY-OSS-CE-RT02-HWNE40E]-Eth-Trunk0
 ipv6 enable
 ip address *********** ***************
 ipv6 address 2408:81B0:7000::5:1000:1000/127
 ospf network-type p2p
 ospf ldp-sync
 ospf timer ldp-sync hold-max-cost 60
 mode lacp-static
 lacp timeout fast
 mpls
 mpls ldp
 statistic enable
#
interface Eth-Trunk1
 description To-[HAZZ-ZYL-N40E-E1]-Eth-Trunk10_Bnet
 mode lacp-static
 lacp timeout fast
作用：
命令用来进入已经存在的接口，或创建并进入逻辑接口
参数说明：
mtu：最大传输单元
ospf：命令用来创建并运行OSPF进程。
mode { lacp-static | manual [ load-balance ] }：
lacp-static	：指定Eth-Trunk接口工作模式为静态LACP模式。
manual：指定Eth-Trunk接口工作模式为手工模式。
load-balance：指定Eth-Trunk接口工作模式为手工负载分担模式。
mpls：命令用来使能本节点的MPLS能力，并进入MPLS视图。

-- Gig物理口配置
配置：
interface GigabitEthernet0/0/0
 speed auto
 duplex auto
 description TO-[ZY1B3F1-08P15L41U-Pub-HW5320-DWSW01]-GE0/0/35+GE0/0/36_beiyong
 undo shutdown
 ip binding vpn-instance mgt
 ip address ********** *************
#
interface GigabitEthernet1/0/0
 shutdown
 ipv6 enable
 undo dcn
 statistic enable
作用：
参数说明：

-- HP-GE特殊接口
配置：
interface HP-GE17/3/0
 shutdown
#
interface HP-GE17/3/1
 shutdown
作用：
参数说明：

-- bfd bfd trunck 高可用检测
配置：
bfd bfd_trunk0:1 bind peer-ip default-ip interface 100GE1/1/0
 discriminator local 1101
 discriminator remote 101
 min-tx-interval 50
 min-rx-interval 50
 process-interface-status
#
bfd bfd_trunk0:2 bind peer-ip default-ip interface 100GE1/1/1
 discriminator local 1102
 discriminator remote 102
 min-tx-interval 50
 min-rx-interval 50
 process-interface-status
作用：
参数说明：

-- dgp 动态路由协议（peer等）骨干网
配置：
bgp 65159
 router-id *************
 undo default ipv4-unicast
 private-4-byte-as enable
 peer ************* as-number 65159
 peer ************* description To ZYJD1B3F2-04P17L03U-CK-NE40E-X16A-2
 peer ************* connect-interface LoopBack0
 peer ************* bfd min-tx-interval 100 min-rx-interval 100
 peer ************* bfd enable
 #
 ipv4-family unicast
  undo synchronization
  undo peer ************* enable
 #
 ipv4-family vpnv4
  policy vpn-target
  peer ************* enable
  peer ************* reflect-client
  peer ************* next-hop-local
 #
 ipv4-family vpn-instance 5G-TF_A
  peer ************ as-number 65159
  peer ************ description To_DianLianGJGX-5G-TN
  peer ************ bfd min-tx-interval 100 min-rx-interval 100
  peer ************ bfd enable
  peer ************ next-hop-local
  peer ************** as-number 9929
  peer ************** description To_DianLianGJGX-5G-TN
  peer ************** bfd min-tx-interval 100 min-rx-interval 100
  peer ************** bfd enable
  peer ************** ip-prefix DianLianGJGX import
  peer ************** ip-prefix DianLianGJGX export
 #
 ipv4-family vpn-instance 5GC_MGMN_B
  peer *********** as-number 38351
  peer *********** description To-VPN-5GC-MGMN
  peer *********** bfd min
  …………
作用：
参数说明：

-- ospf 动态路由协议 内网
配置：
ospf 10 router-id *************
 bfd all-interfaces enable
 bfd all-interfaces min-tx-interval 50 min-rx-interval 50
 opaque-capability enable
 area 0.0.0.0
  network *********** *******
  network ************* 0.0.0.0
作用：
参数说明：

-- route-policy 路由策略
配置：
route-policy IMS_VPN_out permit node 10
 if-match ip-prefix pl_IMS_VPN
#
route-policy add_med permit node 10
#
route-policy anet deny node 10
 if-match ip-prefix anet
#
route-policy anet permit node 20
#
route-policy anet_ew_in deny node 10
 if-match ip-prefix anet_ew_in
#
route-policy anet_ew_in permit node 20
#
route-policy anet_ew_out permit node 10
 if-match ip-prefix anet_ew_out
#
route-policy bnet_import_direct permit node 10
 if-match ip-prefix bnet_import_direct
 apply tag 300
#
route-policy denydc7 deny node 10
 if-match ip-prefix dc7
#
route-policy denydc7 permit node 40
#
route-policy rp_5GC_MGMN_out permit node 10
 if-match ip-prefix pl_5GC_MGMN
#
route-policy rp_5G_MEC_in permit node 10
 if-match ip-prefix pl_5G_MEC
 ……………………
作用：
参数说明：

-- ip ip-prefix 前缀列表（ipv4）
配置：
ip ip-prefix 5gc_nef index 10 permit ********* 24 greater-equal 24 less-equal 32
ip ip-prefix 5gc_nef index 20 permit ********* 24 greater-equal 24 less-equal 32
ip ip-prefix 5gc_nef index 30 permit ********* 24 greater-equal 24 less-equal 32
ip ip-prefix 5gc_nef index 40 permit ********* 24 greater-equal 24 less-equal 32
ip ip-prefix 5gc_nef index 50 permit ********* 24 greater-equal 24 less-equal 32
ip ip-prefix 5gc_tanzhen_in index 10 deny 10.0.0.0 16 greater-equal 16 less-equal 24
ip ip-prefix 5gc_tanzhen_in index 20 permit 10.0.0.0 12 greater-equal 16 less-equal 24
ip ip-prefix 5gc_tanzhen_in index 30 permit ********* 14 greater-equal 16 less-equal 24
ip ip-prefix 5gc_tanzhen_in index 40 permit ********** 24
ip ip-prefix 5gc_tanzhen_in index 50 permit ********** 24
ip ip-prefix 5gc_tanzhen_out index 10 permit ************** 26
ip ip-prefix BSS-SN_A_Network index 10 permit ************** 32
ip ip-prefix BSS-SN_A_Network index 20 permit ************** 32
ip ip-prefix BSS-SN_A_Network index 30 permit ************* 32
ip ip-prefix BSS-SN_A_Network index 40 permit ************* 32
ip ip-prefix BSS-SN_A_Network index 50 permit ************ 32
ip ip-prefix BSS-SN_A_Network index 60 permit ************ 32
ip ip-prefix DianLianGJGX index 10 permit ************ 24
ip ip-prefix DianLianGJGX index 20 permit ********** 24
ip ip-prefix GLW_BVMP-H_in index 10 permit *********** 24 greater-equal 24 less-equal 32
ip ip-prefix GLW_BVMP-H_in index 20 permit 1
………………
作用：
参数说明：

-- ip route-static 静态路由，区分ospf、DGP
配置：
ip route-static vpn-instance mgt 0.0.0.0 0.0.0.0 ************ description MGMT
ip route-static vpn-instance BSS-SN_A ************** *************** ************** description TO-SanXi-BSS_2002935085
ip route-static vpn-instance BSS-SN_A ************** *************** ************** description 2002943109
ip route-static vpn-instance BSS-SN_A ************* *************** ************** description TO-JILIN-BSS
ip route-static vpn-instance BSS-SN_A ************* *************** ************** description TO-JILIN-BSS
ip route-static vpn-instance BSS-SN_A ************ *************** ************** description TO-YUNNAN-BSS
ip route-static vpn-instance BSS-SN_A ************ *************** ************** description TO-YUNNAN-BSS
ip route-static vpn-instance OSS-SN_A ************* *************** ************** description TO-ShanXi-taiyuan-OSS
ip route-static vpn-i
………………
作用：
参数说明：


-- ip ipv6-prefix前缀列表（ipv6）
配置：
ip ipv6-prefix 5gc_mgmn index 10 permit 2408:81B0:A00:1:: 64
ip ipv6-prefix 5gc_mgmn index 100 permit 2408:81A0:8000:4000:: 60
ip ipv6-prefix 5gc_mgmn index 110 permit 2408:8142:60FF:FB0C:: 64
ip ipv6-prefix 5gc_mgmn index 115 permit 2408:8142:60FF:FB0B:: 64
ip ipv6-prefix 5gc_mgmn index 125 permit 2408:81A0:8000:6000:: 60
ip ipv6-prefix 5gc_mgmn index 135 permit 2408:81A0:C000:8000:: 52
ip ipv6-prefix 5gc_mgmn index 145 permit 2408:81A0:2000:4000:: 64
ip ipv6-prefix 5gc_mgmn index 155 permit 2408:81A0:8000:8000:: 60
ip ipv6-prefix 5gc_mgmn index 165 permit 2408:81A0:8000:A000:: 60
ip ipv6-prefix 5gc_mgmn index 175 permit 2408:8142:E0FF:FB11:: 64
…………………
作用：
参数说明：



-- snmp-agent 网管对接 告警信息对接
配置：
snmp-agent
snmp-agent local-engineid 800007DB032C52AFDAC812
snmp-agent community read cipher %^%#nO,02ih6/%FgKoBy<%`!xbmhW0dHV(|q<(I2X3y/>CdYPDZ2IQY_>YRE03|H5bMb%ZtCm)irCMJ{U)G,%^%# acl 2222 alias __CommunityAliasName_01_59498
#
snmp-agent sys-info version all
snmp-agent target-host host-name __targetHost_10_58385 trap address udp-domain *********** vpn-instance mgt params securityname cipher %^%#\@'fCx]9s$2/,y9.MT!7bN:R!B.xtFnfZ!J80C"D%^%#
snmp-agent target-host host-name __targetHost_11_16218 trap address udp-domain ************* vpn-instance mgt params securityname cipher %^%#M^9WW|2B3"!GB09'zhtQ2%tQ-0f9l:&!m7@8/]l.%^%#
snmp-agent target-host host-name __targetHost_1_33122 trap address udp-domain ************ vpn-instance mgt params securityname cipher %^%#,3{d6QsSL4=oYI5*OMfAY>}10@_7V)zI-TD',@m>%^%#
snmp-agent target-host host-name __targetHost_2_57941 trap address udp-domain ************ vpn-instance mgt params securityname cipher %^%#:u@VWKJ:n1'd6cS{JCN*a8jv<V(<Q-(CAR505k95%^%#
snmp-agent target-host host-name __targetHost_3_8470 trap address udp-domain ************ vpn-instance mgt params securityname cipher %^%#jji)38i4L;ozG_:u3M(9vATC%'2jZ/D;ZA8HsgF#%^%#
snmp-agent target-host host-name __targetHost_4_56324 trap address udp-domain ************ vpn-instance mgt params securityname cipher %^%#SY^C/vw3h/{.uiCGict,r^34,TSpu:Cmf0Mm|wF<%^%#
#
………………
作用：
参数说明：



-- ssh server 链接网络设备用，登录
配置：
stelnet server enable

ssh server rsa-key min-length 3072
ssh server-source all-interface
ssh ipv6 server-source all-interface
ssh authorization-type default aaa
#
ssh server cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr
ssh server hmac sha2_512 sha2_256
ssh server key-exchange dh_group_exchange_sha256 dh_group_exchange_sha1 dh_group14_sha1 ecdh_sha2_nistp256 ecdh_sha2_nistp384 ecdh_sha2_nistp521
#
ssh server publickey ecc rsa rsa_sha2_256 rsa_sha2_512
#
ssh server dh-exchange min-len 3072
#
ssh client first-time enable
#
ssh client publickey ecc rsa rsa_sha2_256 rsa_sha2_512
#
ssh client cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr
ssh client hmac sha2_512 sha2_256
ssh client key-exchange dh_group_exchange_sha256 dh_group_exchange_sha1 dh_group14_sha1 ecdh_sha2_nistp256 ecdh_sha2_nistp384 ecdh_sha2_nistp521
#
command-privilege level 1 view global display current-configuration
command-privilege level 1 view shell screen-length
#
user-interface maximum-vty 9
#
user-interface con 0
 authentication-mode password
 
#
user-interface aux 0
 undo shell
#
user-interface vty 0 8
 authentication-mode aaa
 ……………………
作用：
参数说明：

