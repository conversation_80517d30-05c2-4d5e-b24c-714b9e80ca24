#
//这是另一个VXLAN桥接域接口配置，创建Vbdif1143接口，将其绑定到VPC_wlcpyfs_APP_50261 VPN实例，同时配置IPv4地址**************/24和IPv6地址2408:81B0:A00:1:1500:10:0:1/96，设置IPv6邻居发现路由器通告前缀，配置MAC地址为0000-5e00-0102，启用IPv6邻居发现相关功能，并启用VXLAN任播网关和主机信息收集功能。该接口支持双栈(IPv4/IPv6)环境下的VXLAN网络三层网关服务。//
interface Vbdif1143
 ip binding vpn-instance VPC_wlcpyfs_APP_50261
 ipv6 enable
 ip address ************** ***************
 ipv6 address 2408:81B0:A00:1:1500:10:0:1/96
 ipv6 nd ra prefix 2408:81B0:A00:1:1500:10::/96 0 0 no-autoconfig off-link
 mac-address 0000-5e00-0102
 ipv6 nd ra halt disable
 ipv6 nd autoconfig managed-address-flag
 ipv6 nd autoconfig other-flag
 ipv6 nd collect host enable
 vxlan anycast-gateway enable
 arp collect host enable
