#
//这是边界网关协议(BGP)配置的开始部分，配置BGP进程100，设置路由器ID为************，配置四个BGP对等体************、************、************和************，它们都属于AS 100（与本地AS相同，为IBGP对等体），并使用LoopBack0接口作为连接接口。BGP用于在自治系统之间或内部交换路由信息，在VXLAN EVPN网络中承担控制平面的角色，负责VTEP发现和MAC/IP路由信息的分发。//
bgp 100
 router-id ************
 peer ************ as-number 100
 peer ************ connect-interface LoopBack0
 peer ************ as-number 100
 peer ************ connect-interface LoopBack0
 peer ************ as-number 100
 peer ************ connect-interface LoopBack0
 peer ************ as-number 100
 peer ************ connect-interface LoopBack0
 #
 //这是BGP IPv4单播地址族配置，启用四个BGP对等体************、************、************和************在IPv4单播地址族下的能力，使它们能够交换IPv4单播路由信息。//
 ipv4-family unicast
  peer ************ enable
  peer ************ enable
  peer ************ enable
  peer ************ enable
 #
 //这是BGP VPN实例IPv4地址族配置，配置ManageOne_0000011 VPN实例的IPv4地址族，允许导入默认路由，导入直连和静态路由，设置最大负载均衡路径数为32，并将路由通告到L2VPN EVPN地址族。这使得VPN实例内的IPv4路由可以通过EVPN进行分发，实现跨数据中心的VPN路由同步。//
 ipv4-family vpn-instance ManageOne_0000011
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 //这是另一个BGP VPN实例IPv4地址族配置，配置VPC-wlzys-APP_0000013 VPN实例的IPv4地址族，允许导入默认路由，导入直连和静态路由，设置最大负载均衡路径数为32，并将路由通告到L2VPN EVPN地址族。这使得该VPC应用VPN实例内的IPv4路由可以通过EVPN进行分发。//
 ipv4-family vpn-instance VPC-wlzys-APP_0000013
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 //这是BGP VPN实例IPv6地址族配置，配置VPC-wlzys-APP_0000013 VPN实例的IPv6地址族，允许导入默认路由，导入直连和静态路由，设置最大负载均衡路径数为32，并将路由通告到L2VPN EVPN地址族。这使得该VPC应用VPN实例内的IPv6路由可以通过EVPN进行分发，支持双栈环境下的路由同步。//
 ipv6-family vpn-instance VPC-wlzys-APP_0000013
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 //这是另一个BGP VPN实例IPv6地址族配置，配置VPC_NMS_JT_0000089 VPN实例的IPv6地址族，允许导入默认路由，导入直连和静态路由，设置最大负载均衡路径数为32，并将路由通告到L2VPN EVPN地址族。这使得该网络管理系统VPN实例内的IPv6路由可以通过EVPN进行分发。//
 ipv6-family vpn-instance VPC_NMS_JT_0000089
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 //这是BGP L2VPN EVPN地址族配置，启用VPN目标策略，配置四个BGP对等体************、************、************和************在EVPN地址族下的能力，并允许它们通告集成路由桥接(IRB)和IPv6集成路由桥接(IRBv6)路由。EVPN地址族用于在VXLAN网络中分发MAC地址、IP地址和路由信息，支持二层和三层服务的融合，实现虚拟机迁移、多活数据中心等高级网络功能。//
 l2vpn-family evpn
  policy vpn-target
  peer ************ enable
  peer ************ advertise irb
  peer ************ advertise irbv6
  peer ************ enable
  peer ************ advertise irb
  peer ************ advertise irbv6
  peer ************ enable
  peer ************ advertise irb
  peer ************ advertise irbv6
  peer ************ enable
  peer ************ advertise irb
  peer ************ advertise irbv6
