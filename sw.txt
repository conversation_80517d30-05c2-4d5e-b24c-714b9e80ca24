
SW>display cur
!Software Version V200R005C10SPC800
!Last configuration was updated at 2025-01-09 11:28:08+08:00 by netconf
!Last configuration was saved at 2025-01-15 01:50:44+08:00
#
clock timezone BJ add 08:00:00
#
sysname SW
#
dfs-group 1
 priority 150
 source ip *********** 
#
info-center source default channel 2 log level warning
info-center loghost source LoopBack0
info-center loghost ************
#
system resource large-route
#
device board 1 board-type CE6855-48S6Q-HI
#
drop-profile default
#
dcb pfc
#
dcb ets-profile default
#
ntp ipv6 server disable
ntp source-interface LoopBack0
ntp unicast-server ********** preferred
ntp unicast-server **********
#
assign forward layer-3 resource large-overlay
#
arp resource-saving-mode
#
assign forward ipv6 longer-mask resource 1024
#
vlan reserved for main-interface 4060 to 4063
#
mac-address flapping periodical trap enable
#
stp mode rstp
stp v-stp enable
#
evpn-overlay enable
#
lacp m-lag system-id 00e0-fc00-0001
#
telnet server disable
telnet ipv6 server disable
#
diffserv domain default
#
ip vpn-instance ManageOne_0000011
 ipv4-family
  route-distinguisher 78:50017
  vpn-target 0:50017 export-extcommunity
  vpn-target 0:50017 export-extcommunity evpn
  vpn-target 0:50017 import-extcommunity
  vpn-target 0:50017 import-extcommunity evpn
 description ManageOne(0c7f69422055424a8734d71ce7a44979)-fusionsphere_ManageOne
 vxlan vni 50017
#
ip vpn-instance VPC-wlzys-APP_0000013
 ipv4-family
  route-distinguisher 78:50019
  vpn-target 0:50019 export-extcommunity
  vpn-target 0:50019 export-extcommunity evpn
  vpn-target 0:50019 import-extcommunity
  vpn-target 0:50019 import-extcommunity evpn
 description weiwo(9f52ee18f3bb41e3825280792429a1b1)-fusionsphere_VPC-wlzys-APP
 ipv6-family 
  route-distinguisher 78:50019
  vpn-target 0:50019 export-extcommunity
  vpn-target 0:50019 export-extcommunity evpn
  vpn-target 0:50019 import-extcommunity
  vpn-target 0:50019 import-extcommunity evpn
 vxlan vni 50019
#
ip vpn-instance VPC_NMS_JT_0000089
 ipv4-family
  route-distinguisher 78:50187
  vpn-target 0:50187 export-extcommunity
  vpn-target 0:50187 export-extcommunity evpn
  vpn-target 0:50187 import-extcommunity
  vpn-target 0:50187 import-extcommunity evpn
 description cywlwcps(4364fd63fdee454d9dfc431340a378a1)-fusionsphere_VPC_NMS_JT
 ipv6-family 
  route-distinguisher 78:50187
  vpn-target 0:50187 export-extcommunity
  vpn-target 0:50187 export-extcommunity evpn
  vpn-target 0:50187 import-extcommunity
  vpn-target 0:50187 import-extcommunity evpn
 vxlan vni 50187
#
ip vpn-instance VPC_ZY_WLW_APP_0000083
 ipv4-family
  route-distinguisher 78:50080
  vpn-target 0:50080 export-extcommunity
  vpn-target 0:50080 export-extcommunity evpn
  vpn-target 0:50080 import-extcommunity
  vpn-target 0:50080 import-extcommunity evpn
 description osswlws(b8cc1393df274aaab561e3b6b5f22892)-fusionsphere_VPC_ZY_WLW_APP
 vxlan vni 50080
#
ip vpn-instance VPC_cywlwcps_APP_0000068
 ipv4-family
  route-distinguisher 78:50139
  vpn-target 0:50139 export-extcommunity
  vpn-target 0:50139 export-extcommunity evpn
  vpn-target 0:50139 import-extcommunity
  vpn-target 0:50139 import-extcommunity evpn
 description cywlwcps(4364fd63fdee454d9dfc431340a378a1)-fusionsphere_VPC_cywlwcps_APP
 ipv6-family 
  route-distinguisher 78:50139
  vpn-target 0:50139 export-extcommunity
  vpn-target 0:50139 export-extcommunity evpn
  vpn-target 0:50139 import-extcommunity
  vpn-target 0:50139 import-extcommunity evpn
 vxlan vni 50139
#
ip vpn-instance VPC_cywlwcps_DB_0000069
 ipv4-family
  route-distinguisher 78:50140
  vpn-target 0:50140 export-extcommunity
  vpn-target 0:50140 export-extcommunity evpn
  vpn-target 0:50140 import-extcommunity
  vpn-target 0:50140 import-extcommunity evpn
 description cywlwcps(4364fd63fdee454d9dfc431340a378a1)-fusionsphere_VPC_cywlwcps_DB
 ipv6-family 
  route-distinguisher 78:50140
  vpn-target 0:50140 export-extcommunity
  vpn-target 0:50140 export-extcommunity evpn
  vpn-target 0:50140 import-extcommunity
  vpn-target 0:50140 import-extcommunity evpn
 vxlan vni 50140
#
ip vpn-instance VPC_cywlwcps_DMZ_0000070
 ipv4-family
  route-distinguisher 78:50141
  vpn-target 0:50141 export-extcommunity
  vpn-target 0:50141 export-extcommunity evpn
  vpn-target 0:50141 import-extcommunity
  vpn-target 0:50141 import-extcommunity evpn
 description cywlwcps(4364fd63fdee454d9dfc431340a378a1)-fusionsphere_VPC_cywlwcps_DMZ
 ipv6-family 
  route-distinguisher 78:50141
  vpn-target 0:50141 export-extcommunity
  vpn-target 0:50141 export-extcommunity evpn
  vpn-target 0:50141 import-extcommunity
  vpn-target 0:50141 import-extcommunity evpn
 vxlan vni 50141
#
ip vpn-instance VPC_ex_IMS_xuanling_0000108
 ipv4-family
  route-distinguisher 78:50225
  vpn-target 0:50225 export-extcommunity
  vpn-target 0:50225 export-extcommunity evpn
  vpn-target 0:50225 import-extcommunity
  vpn-target 0:50225 import-extcommunity evpn
 description jsAIzx(ad0bd9485dab4957aec9ff47d201a349)-fusionsphere_VPC_ex_IMS_xuanling
 ipv6-family 
  route-distinguisher 78:50225
  vpn-target 0:50225 export-extcommunity
  vpn-target 0:50225 export-extcommunity evpn
  vpn-target 0:50225 import-extcommunity
  vpn-target 0:50225 import-extcommunity evpn
 vxlan vni 50225
#
ip vpn-instance VPC_fssyw_DB_0000024
 ipv4-family
  route-distinguisher 78:50032
  vpn-target 0:50032 export-extcommunity
  vpn-target 0:50032 export-extcommunity evpn
  vpn-target 0:50032 import-extcommunity
  vpn-target 0:50032 import-extcommunity evpn
 description osswlws(b8cc1393df274aaab561e3b6b5f22892)-fusionsphere_VPC_osswlws_DB
 ipv6-family 
  route-distinguisher 78:50032
  vpn-target 0:50032 export-extcommunity
  vpn-target 0:50032 export-extcommunity evpn
  vpn-target 0:50032 import-extcommunity
  vpn-target 0:50032 import-extcommunity evpn
 vxlan vni 50032
#
ip vpn-instance VPC_fssyw_DMZ_0000025
 ipv4-family
  route-distinguisher 78:50033
  vpn-target 0:50033 export-extcommunity
  vpn-target 0:50033 export-extcommunity evpn
  vpn-target 0:50033 import-extcommunity
  vpn-target 0:50033 import-extcommunity evpn
 description osswlws(b8cc1393df274aaab561e3b6b5f22892)-fusionsphere_VPC_osswlws_DMZ
 ipv6-family 
  route-distinguisher 78:50033
  vpn-target 0:50033 export-extcommunity
  vpn-target 0:50033 export-extcommunity evpn
  vpn-target 0:50033 import-extcommunity
  vpn-target 0:50033 import-extcommunity evpn
 vxlan vni 50033
#
ip vpn-instance VPC_hxwcps_APP_0000034
 ipv4-family
  route-distinguisher 78:50042
  vpn-target 0:50042 export-extcommunity
  vpn-target 0:50042 export-extcommunity evpn
  vpn-target 0:50042 import-extcommunity
  vpn-target 0:50042 import-extcommunity evpn
 description hxwcps(b35997478a4642d8812e0bbd93d54e93)-fusionsphere_VPC_hxwcps_APP
 ipv6-family 
  route-distinguisher 78:50042
  vpn-target 0:50042 export-extcommunity
  vpn-target 0:50042 export-extcommunity evpn
  vpn-target 0:50042 import-extcommunity
  vpn-target 0:50042 import-extcommunity evpn
 vxlan vni 50042
#
ip vpn-instance VPC_hxwcps_DB_0000064
 ipv4-family
  route-distinguisher 78:50132
  vpn-target 0:50132 export-extcommunity
  vpn-target 0:50132 export-extcommunity evpn
  vpn-target 0:50132 import-extcommunity
  vpn-target 0:50132 import-extcommunity evpn
 description hxwcps(b35997478a4642d8812e0bbd93d54e93)-fusionsphere_VPC_hxwcps_DB
 ipv6-family 
  route-distinguisher 78:50132
  vpn-target 0:50132 export-extcommunity
  vpn-target 0:50132 export-extcommunity evpn
  vpn-target 0:50132 import-extcommunity
  vpn-target 0:50132 import-extcommunity evpn
 vxlan vni 50132
#
ip vpn-instance VPC_jsAIzx_APP_0000086
 ipv4-family
  route-distinguisher 78:50176
  vpn-target 0:50176 export-extcommunity
  vpn-target 0:50176 export-extcommunity evpn
  vpn-target 0:50176 import-extcommunity
  vpn-target 0:50176 import-extcommunity evpn
 description jsAIzx(ad0bd9485dab4957aec9ff47d201a349)-fusionsphere_VPC_jsAIzx_APP
 vxlan vni 50176
#
ip vpn-instance VPC_jsAIzx_DB1_50513
 ipv4-family
  route-distinguisher 78:50513
  vpn-target 0:50513 export-extcommunity
  vpn-target 0:50513 export-extcommunity evpn
  vpn-target 0:50513 import-extcommunity
  vpn-target 0:50513 import-extcommunity evpn
 description jsAIzx(ad0bd9485dab4957aec9ff47d201a349)-fusionsphere_VPC_jsAIzx_DB1
 ipv6-family 
  route-distinguisher 78:50513
  vpn-target 0:50513 export-extcommunity
  vpn-target 0:50513 export-extcommunity evpn
  vpn-target 0:50513 import-extcommunity
  vpn-target 0:50513 import-extcommunity evpn
 vxlan vni 50513
#
ip vpn-instance VPC_jsAIzx_DB_0000080
 ipv4-family
  route-distinguisher 78:50134
  vpn-target 0:50134 export-extcommunity
  vpn-target 0:50134 export-extcommunity evpn
  vpn-target 0:50134 import-extcommunity
  vpn-target 0:50134 import-extcommunity evpn
 description jsAIzx(ad0bd9485dab4957aec9ff47d201a349)-fusionsphere_VPC_jsAIzx_DB
 vxlan vni 50134
#
ip vpn-instance VPC_lt_APP_0000023
 ipv4-family
  route-distinguisher 78:50031
  vpn-target 0:50031 export-extcommunity
  vpn-target 0:50031 export-extcommunity evpn
  vpn-target 0:50031 import-extcommunity
  vpn-target 0:50031 import-extcommunity evpn
 description liantong(54323221bdcb4997b3b4c8280dae2712)-fusionsphere_VPC_lt_APP
 ipv6-family 
  route-distinguisher 78:50031
  vpn-target 0:50031 export-extcommunity
  vpn-target 0:50031 export-extcommunity evpn
  vpn-target 0:50031 import-extcommunity
  vpn-target 0:50031 import-extcommunity evpn
 vxlan vni 50031
#
ip vpn-instance VPC_lt_DB_0000022
 ipv4-family
  route-distinguisher 78:50030
  vpn-target 0:50030 export-extcommunity
  vpn-target 0:50030 export-extcommunity evpn
  vpn-target 0:50030 import-extcommunity
  vpn-target 0:50030 import-extcommunity evpn
 description liantong(54323221bdcb4997b3b4c8280dae2712)-fusionsphere_VPC_lt_DB
 vxlan vni 50030
#
ip vpn-instance VPC_neimenggu_0000084
 ipv4-family
  route-distinguisher 78:50172
  vpn-target 0:50172 export-extcommunity
  vpn-target 0:50172 export-extcommunity evpn
  vpn-target 0:50172 import-extcommunity
  vpn-target 0:50172 import-extcommunity evpn
 description (7ecc04f3e9574221874f803affcb5b0f)-fusionsphere_VPC_neimenggu
 vxlan vni 50172
#
ip vpn-instance VPC_ossnls_APP_0000038
 ipv4-family
  route-distinguisher 78:50059
  vpn-target 0:50059 export-extcommunity
  vpn-target 0:50059 export-extcommunity evpn
  vpn-target 0:50059 import-extcommunity
  vpn-target 0:50059 import-extcommunity evpn
 description ossnls(a0627790903b45c7ba734def83440775)-fusionsphere_VPC_ossnls_APP
 vxlan vni 50059
#
ip vpn-instance VPC_osswlws_APP_0000033
 ipv4-family
  route-distinguisher 78:50041
  vpn-target 0:50041 export-extcommunity
  vpn-target 0:50041 export-extcommunity evpn
  vpn-target 0:50041 import-extcommunity
  vpn-target 0:50041 import-extcommunity evpn
 description osswlws(b8cc1393df274aaab561e3b6b5f22892)-fusionsphere_VPC_osswlws_APP
 ipv6-family 
  route-distinguisher 78:50041
  vpn-target 0:50041 export-extcommunity
  vpn-target 0:50041 export-extcommunity evpn
  vpn-target 0:50041 import-extcommunity
  vpn-target 0:50041 import-extcommunity evpn
 vxlan vni 50041
#
ip vpn-instance VPC_otthzs_APP_0000019
 ipv4-family
  route-distinguisher 78:50027
  vpn-target 0:50027 export-extcommunity
  vpn-target 0:50027 export-extcommunity evpn
  vpn-target 0:50027 import-extcommunity
  vpn-target 0:50027 import-extcommunity evpn
 description otthzs(016e0bfb29644503826a47373c61ade7)-fusionsphere_VPC_otthzs_APP
 ipv6-family 
  route-distinguisher 78:50027
  vpn-target 0:50027 export-extcommunity
  vpn-target 0:50027 export-extcommunity evpn
  vpn-target 0:50027 import-extcommunity
  vpn-target 0:50027 import-extcommunity evpn
 vxlan vni 50027
#
ip vpn-instance VPC_otthzs_DB_0000015
 ipv4-family
  route-distinguisher 78:50023
  vpn-target 0:50023 export-extcommunity
  vpn-target 0:50023 export-extcommunity evpn
  vpn-target 0:50023 import-extcommunity
  vpn-target 0:50023 import-extcommunity evpn
 description otthzs(016e0bfb29644503826a47373c61ade7)-fusionsphere_VPC_otthzs_DB
 ipv6-family 
  route-distinguisher 78:50023
  vpn-target 0:50023 export-extcommunity
  vpn-target 0:50023 export-extcommunity evpn
  vpn-target 0:50023 import-extcommunity
  vpn-target 0:50023 import-extcommunity evpn
 vxlan vni 50023
#
ip vpn-instance VPC_otthzs_DMZ_0000020
 ipv4-family
  route-distinguisher 78:50028
  vpn-target 0:50028 export-extcommunity
  vpn-target 0:50028 export-extcommunity evpn
  vpn-target 0:50028 import-extcommunity
  vpn-target 0:50028 import-extcommunity evpn
 description otthzs(016e0bfb29644503826a47373c61ade7)-fusionsphere_VPC_otthzs_DMZ
 ipv6-family 
  route-distinguisher 78:50028
  vpn-target 0:50028 export-extcommunity
  vpn-target 0:50028 export-extcommunity evpn
  vpn-target 0:50028 import-extcommunity
  vpn-target 0:50028 import-extcommunity evpn
 vxlan vni 50028
#
ip vpn-instance VPC_shAIzx_APP_0000071
 ipv4-family
  route-distinguisher 78:50145
  vpn-target 0:50145 export-extcommunity
  vpn-target 0:50145 export-extcommunity evpn
  vpn-target 0:50145 import-extcommunity
  vpn-target 0:50145 import-extcommunity evpn
 description shAIzx(89c6ac5a2a7d4ccfae2288d768aff7d5)-fusionsphere_VPC_shAIzx_APP
 ipv6-family 
  route-distinguisher 78:50145
  vpn-target 0:50145 export-extcommunity
  vpn-target 0:50145 export-extcommunity evpn
  vpn-target 0:50145 import-extcommunity
  vpn-target 0:50145 import-extcommunity evpn
 vxlan vni 50145
#
ip vpn-instance VPC_shAIzx_DB_0000078
 ipv4-family
  route-distinguisher 78:50162
  vpn-target 0:50162 export-extcommunity
  vpn-target 0:50162 export-extcommunity evpn
  vpn-target 0:50162 import-extcommunity
  vpn-target 0:50162 import-extcommunity evpn
 description shAIzx(89c6ac5a2a7d4ccfae2288d768aff7d5)-fusionsphere_VPC_shAIzx_DB
 vxlan vni 50162
#
ip vpn-instance VPC_shanxi1_0000079
 ipv4-family
  route-distinguisher 78:50165
  vpn-target 0:50165 export-extcommunity
  vpn-target 0:50165 export-extcommunity evpn
  vpn-target 0:50165 import-extcommunity
  vpn-target 0:50165 import-extcommunity evpn
 description shanxi1(dd074ce0a541427ca8f3128068cc8efe)-fusionsphere_VPC_shanxi1
 vxlan vni 50165
#
ip vpn-instance VPC_sichuan_APP_50298
 ipv4-family
  route-distinguisher 78:50298
  vpn-target 0:50298 export-extcommunity
  vpn-target 0:50298 export-extcommunity evpn
  vpn-target 0:50298 import-extcommunity
  vpn-target 0:50298 import-extcommunity evpn
 description (30d530b306474cbb852026d10a4acfc5)-fusionsphere_VPC_sichuan_APP
 ipv6-family 
  route-distinguisher 78:50298
  vpn-target 0:50298 export-extcommunity
  vpn-target 0:50298 export-extcommunity evpn
  vpn-target 0:50298 import-extcommunity
  vpn-target 0:50298 import-extcommunity evpn
 vxlan vni 50298
#
ip vpn-instance VPC_wlaqs_APP_0000058
 ipv4-family
  route-distinguisher 78:50114
  vpn-target 0:50114 export-extcommunity
  vpn-target 0:50114 export-extcommunity evpn
  vpn-target 0:50114 import-extcommunity
  vpn-target 0:50114 import-extcommunity evpn
 description wlaqs(3f59bda8ca164327adee2af1dd0cba4b)-fusionsphere_VPC_wlaqs_APP
 vxlan vni 50114
#
ip vpn-instance VPC_wlaqs_DB_0000048
 ipv4-family
  route-distinguisher 78:50098
  vpn-target 0:50098 export-extcommunity
  vpn-target 0:50098 export-extcommunity evpn
  vpn-target 0:50098 import-extcommunity
  vpn-target 0:50098 import-extcommunity evpn
 description wlaqs(3f59bda8ca164327adee2af1dd0cba4b)-fusionsphere_VPC_wlaqs_DB
 vxlan vni 50098
#
ip vpn-instance VPC_wlaqs_DMZ_0000053
 ipv4-family
  route-distinguisher 78:50112
  vpn-target 0:50112 export-extcommunity
  vpn-target 0:50112 export-extcommunity evpn
  vpn-target 0:50112 import-extcommunity
  vpn-target 0:50112 import-extcommunity evpn
 description wlaqs(3f59bda8ca164327adee2af1dd0cba4b)-fusionsphere_VPC_wlaqs_DMZ
 ipv6-family 
  route-distinguisher 78:50112
  vpn-target 0:50112 export-extcommunity
  vpn-target 0:50112 export-extcommunity evpn
  vpn-target 0:50112 import-extcommunity
  vpn-target 0:50112 import-extcommunity evpn
 vxlan vni 50112
#
ip vpn-instance VPC_wlcpyfs_APP_50261
 ipv4-family
  route-distinguisher 78:50261
  vpn-target 0:50261 export-extcommunity
  vpn-target 0:50261 export-extcommunity evpn
  vpn-target 0:50261 import-extcommunity
  vpn-target 0:50261 import-extcommunity evpn
 description wlcpyfs(8084513823924fccaf2cdb31d94616bd)-fusionsphere_VPC_wlcpyfs_APP
 ipv6-family 
  route-distinguisher 78:50261
  vpn-target 0:50261 export-extcommunity
  vpn-target 0:50261 export-extcommunity evpn
  vpn-target 0:50261 import-extcommunity
  vpn-target 0:50261 import-extcommunity evpn
 vxlan vni 50261
#
ip vpn-instance VPC_wlcpyfs_DB_50262
 ipv4-family
  route-distinguisher 78:50262
  vpn-target 0:50262 export-extcommunity
  vpn-target 0:50262 export-extcommunity evpn
  vpn-target 0:50262 import-extcommunity
  vpn-target 0:50262 import-extcommunity evpn
 description wlcpyfs(8084513823924fccaf2cdb31d94616bd)-fusionsphere_VPC_wlcpyfs_DB
 ipv6-family 
  route-distinguisher 78:50262
  vpn-target 0:50262 export-extcommunity
  vpn-target 0:50262 export-extcommunity evpn
  vpn-target 0:50262 import-extcommunity
  vpn-target 0:50262 import-extcommunity evpn
 vxlan vni 50262
#
ip vpn-instance VPC_wlcpyfs_DMZ_50263
 ipv4-family
  route-distinguisher 78:50263
  vpn-target 0:50263 export-extcommunity
  vpn-target 0:50263 export-extcommunity evpn
  vpn-target 0:50263 import-extcommunity
  vpn-target 0:50263 import-extcommunity evpn
 description wlcpyfs(8084513823924fccaf2cdb31d94616bd)-fusionsphere_VPC_wlcpyfs_DMZ
 ipv6-family 
  route-distinguisher 78:50263
  vpn-target 0:50263 export-extcommunity
  vpn-target 0:50263 export-extcommunity evpn
  vpn-target 0:50263 import-extcommunity
  vpn-target 0:50263 import-extcommunity evpn
 vxlan vni 50263
#
ip vpn-instance VPC_wlcpyfs_IMS_50393
 ipv4-family
  route-distinguisher 78:50393
  vpn-target 0:50393 export-extcommunity
  vpn-target 0:50393 export-extcommunity evpn
  vpn-target 0:50393 import-extcommunity
  vpn-target 0:50393 import-extcommunity evpn
 description wlcpyfs(8084513823924fccaf2cdb31d94616bd)-fusionsphere_VPC_wlcpyfs_IMS
 ipv6-family 
  route-distinguisher 78:50393
  vpn-target 0:50393 export-extcommunity
  vpn-target 0:50393 export-extcommunity evpn
  vpn-target 0:50393 import-extcommunity
  vpn-target 0:50393 import-extcommunity evpn
 vxlan vni 50393
#
ip vpn-instance VPC_wlcpyfs_ZNZX_50269
 ipv4-family
  route-distinguisher 78:50269
  vpn-target 0:50269 export-extcommunity
  vpn-target 0:50269 export-extcommunity evpn
  vpn-target 0:50269 import-extcommunity
  vpn-target 0:50269 import-extcommunity evpn
 description wlcpyfs(8084513823924fccaf2cdb31d94616bd)-fusionsphere_VPC_wlcpyfs_ZNZX
 ipv6-family 
  route-distinguisher 78:50269
  vpn-target 0:50269 export-extcommunity
  vpn-target 0:50269 export-extcommunity evpn
  vpn-target 0:50269 import-extcommunity
  vpn-target 0:50269 import-extcommunity evpn
 vxlan vni 50269
#
ip vpn-instance VPC_wlfzb_APP_0000066
 ipv4-family
  route-distinguisher 78:50136
  vpn-target 0:50136 export-extcommunity
  vpn-target 0:50136 export-extcommunity evpn
  vpn-target 0:50136 import-extcommunity
  vpn-target 0:50136 import-extcommunity evpn
 description (8a1bb6a2b32d43f9a2578a1630b70cee)-fusionsphere_VPC_wlfzb_APP
 vxlan vni 50136
#
ip vpn-instance VPC_wlsjs_APP_0000032
 ipv4-family
  route-distinguisher 78:50040
  vpn-target 0:50040 export-extcommunity
  vpn-target 0:50040 export-extcommunity evpn
  vpn-target 0:50040 import-extcommunity
  vpn-target 0:50040 import-extcommunity evpn
 description wlsjs(28f94bdd280a430184bf8cb0b075236c)-fusionsphere_VPC_wlsjs_APP
 ipv6-family 
  route-distinguisher 78:50040
  vpn-target 0:50040 export-extcommunity
  vpn-target 0:50040 export-extcommunity evpn
  vpn-target 0:50040 import-extcommunity
  vpn-target 0:50040 import-extcommunity evpn
 vxlan vni 50040
#
ip vpn-instance VPC_wlzys_DB_0000014
 ipv4-family
  route-distinguisher 78:50021
  vpn-target 0:50021 export-extcommunity
  vpn-target 0:50021 export-extcommunity evpn
  vpn-target 0:50021 import-extcommunity
  vpn-target 0:50021 import-extcommunity evpn
 description weiwo(9f52ee18f3bb41e3825280792429a1b1)-fusionsphere_VPC_wlzys_DB
 vxlan vni 50021
#
ip vpn-instance VPC_wlzys_DMZ_0000012
 ipv4-family
  route-distinguisher 78:50018
  vpn-target 0:50018 export-extcommunity
  vpn-target 0:50018 export-extcommunity evpn
  vpn-target 0:50018 import-extcommunity
  vpn-target 0:50018 import-extcommunity evpn
 description weiwo(9f52ee18f3bb41e3825280792429a1b1)-fusionsphere_VPC_wlzys_DMZ
 ipv6-family 
  route-distinguisher 78:50018
  vpn-target 0:50018 export-extcommunity
  vpn-target 0:50018 export-extcommunity evpn
  vpn-target 0:50018 import-extcommunity
  vpn-target 0:50018 import-extcommunity evpn
 vxlan vni 50018
#
ip vpn-instance VPC_yuntian_APP_0000017
 ipv4-family
  route-distinguisher 78:50025
  vpn-target 0:50025 export-extcommunity
  vpn-target 0:50025 export-extcommunity evpn
  vpn-target 0:50025 import-extcommunity
  vpn-target 0:50025 import-extcommunity evpn
 description yuntian(84f9b24d90304c8d9ef9790a8b204864)-fusionsphere_VPC_yuntian_APP
 vxlan vni 50025
#
ip vpn-instance VPC_yuntian_DB_0000016
 ipv4-family
  route-distinguisher 78:50024
  vpn-target 0:50024 export-extcommunity
  vpn-target 0:50024 export-extcommunity evpn
  vpn-target 0:50024 import-extcommunity
  vpn-target 0:50024 import-extcommunity evpn
 description yuntian(84f9b24d90304c8d9ef9790a8b204864)-fusionsphere_VPC_yuntian_DB
 vxlan vni 50024
#
ip vpn-instance VPC_yuntian_DMZ_0000018
 ipv4-family
  route-distinguisher 78:50026
  vpn-target 0:50026 export-extcommunity
  vpn-target 0:50026 export-extcommunity evpn
  vpn-target 0:50026 import-extcommunity
  vpn-target 0:50026 import-extcommunity evpn
 description yuntian(84f9b24d90304c8d9ef9790a8b204864)-fusionsphere_VPC_yuntian_DMZ
 ipv6-family 
  route-distinguisher 78:50026
  vpn-target 0:50026 export-extcommunity
  vpn-target 0:50026 export-extcommunity evpn
  vpn-target 0:50026 import-extcommunity
  vpn-target 0:50026 import-extcommunity evpn
 vxlan vni 50026
#
ip vpn-instance mgmt
 ipv4-family
  route-distinguisher 10:10
#
ip vpn-instance vpc-552a_0000005
 ipv4-family
  route-distinguisher 78:50002
  vpn-target 0:50002 export-extcommunity
  vpn-target 0:50002 export-extcommunity evpn
  vpn-target 0:50002 import-extcommunity
  vpn-target 0:50002 import-extcommunity evpn
 description FCD_project01(5e66a21a979743198549ac8e633992f7)-fusionsphere_vpc-552a
 vxlan vni 50002
#
ip vpn-instance vpc-a3ee_0000006
 ipv4-family
  route-distinguisher 78:50003
  vpn-target 0:50003 export-extcommunity
  vpn-target 0:50003 export-extcommunity evpn
  vpn-target 0:50003 import-extcommunity
  vpn-target 0:50003 import-extcommunity evpn
 description IES_VDC(3d1ca605ddcb4bce9a5a498cd98ccbfe)-fusionsphere_vpc-a3ee
 vxlan vni 50003
#
ip vpn-instance vpc-dzyuwDMZ_0000009
 ipv4-family
  route-distinguisher 78:50001
  vpn-target 0:50001 export-extcommunity
  vpn-target 0:50001 export-extcommunity evpn
  vpn-target 0:50001 import-extcommunity
  vpn-target 0:50001 import-extcommunity evpn
 description dzyw_appweb(756ecd40f83b420a9ed24185ca7294a6)-fusionsphere_vpc-dzyuwDMZ
 vxlan vni 50001
#
ip vpn-instance vpc-gansu_0000044
 ipv4-family
  route-distinguisher 78:50093
  vpn-target 0:50093 export-extcommunity
  vpn-target 0:50093 export-extcommunity evpn
  vpn-target 0:50093 import-extcommunity
  vpn-target 0:50093 import-extcommunity evpn
 description gansu(d545f8ce480b447db451c738d5bae419)-fusionsphere_vpc-gansu
 vxlan vni 50093
#
ip vpn-instance vpc-hebei_0000060
 ipv4-family
  route-distinguisher 78:50113
  vpn-target 0:50113 export-extcommunity
  vpn-target 0:50113 export-extcommunity evpn
  vpn-target 0:50113 import-extcommunity
  vpn-target 0:50113 import-extcommunity evpn
 description hebei(35d4946c6f024fedaaf1fd6baa6b74be)-fusionsphere_vpc-hebei
 vxlan vni 50113
#
ip vpn-instance vpc-osswlws-DB2_0000073
 ipv4-family
  route-distinguisher 78:50153
  vpn-target 0:50153 export-extcommunity
  vpn-target 0:50153 export-extcommunity evpn
  vpn-target 0:50153 import-extcommunity
  vpn-target 0:50153 import-extcommunity evpn
 description osswlws(b8cc1393df274aaab561e3b6b5f22892)-fusionsphere_vpc-osswlws-DB2
 vxlan vni 50153
#
ip vpn-instance vpc-qinghai_0000051
 ipv4-family
  route-distinguisher 78:50105
  vpn-target 0:50105 export-extcommunity
  vpn-target 0:50105 export-extcommunity evpn
  vpn-target 0:50105 import-extcommunity
  vpn-target 0:50105 import-extcommunity evpn
 description qinghai(7f7e9c9820d24694a582504b50cf0844)-fusionsphere_vpc-qinghai
 vxlan vni 50105
#
ip vpn-instance vpc-shanxi_0000050
 ipv4-family
  route-distinguisher 78:50102
  vpn-target 0:50102 export-extcommunity
  vpn-target 0:50102 export-extcommunity evpn
  vpn-target 0:50102 import-extcommunity
  vpn-target 0:50102 import-extcommunity evpn
 description (4a4162f8885540998bfae030cfe57855)-fusionsphere_vpc-shanxi
 vxlan vni 50102
#
ip vpn-instance vpc-vdc_0000054
 ipv4-family
  route-distinguisher 78:50120
  vpn-target 0:50120 export-extcommunity
  vpn-target 0:50120 export-extcommunity evpn
  vpn-target 0:50120 import-extcommunity
  vpn-target 0:50120 import-extcommunity evpn
 description VDC(d78f066b92ec4f598b375f4a841feac8)-fusionsphere_vpc-vdc
 ipv6-family 
  route-distinguisher 78:50120
  vpn-target 0:50120 export-extcommunity
  vpn-target 0:50120 export-extcommunity evpn
  vpn-target 0:50120 import-extcommunity
  vpn-target 0:50120 import-extcommunity evpn
 vxlan vni 50120
#
ip vpn-instance vpc-yzys-APP_0000059
 ipv4-family
  route-distinguisher 78:50123
  vpn-target 0:50123 export-extcommunity
  vpn-target 0:50123 export-extcommunity evpn
  vpn-target 0:50123 import-extcommunity
  vpn-target 0:50123 import-extcommunity evpn
 description vdc_yzys(952f7a24d8534e7c9b25b7ca41da3b69)-fusionsphere_vpc-yzys-APP
 ipv6-family 
  route-distinguisher 78:50123
  vpn-target 0:50123 export-extcommunity
  vpn-target 0:50123 export-extcommunity evpn
  vpn-target 0:50123 import-extcommunity
  vpn-target 0:50123 import-extcommunity evpn
 vxlan vni 50123
#
ip vpn-instance vpc-yzys-DMZ_0000057
 ipv4-family
  route-distinguisher 78:50122
  vpn-target 0:50122 export-extcommunity
  vpn-target 0:50122 export-extcommunity evpn
  vpn-target 0:50122 import-extcommunity
  vpn-target 0:50122 import-extcommunity evpn
 description vdc_yzys(952f7a24d8534e7c9b25b7ca41da3b69)-fusionsphere_vpc-yzys-DMZ
 ipv6-family 
  route-distinguisher 78:50122
  vpn-target 0:50122 export-extcommunity
  vpn-target 0:50122 export-extcommunity evpn
  vpn-target 0:50122 import-extcommunity
  vpn-target 0:50122 import-extcommunity evpn
 vxlan vni 50122
#
sdn agent
 controller-ip ************
  openflow agent
   transport-address ***********
 controller-ip ************
  openflow agent
   transport-address ***********
#
bridge-domain 31
 vxlan vni 5037
 evpn
  route-distinguisher 78:5037
  vpn-target 0:5037 export-extcommunity
  vpn-target 0:5037 import-extcommunity
#
bridge-domain 33
 vxlan vni 9762
 evpn
  route-distinguisher 78:9762
  vpn-target 0:9762 export-extcommunity
  vpn-target 0:50002 export-extcommunity
  vpn-target 0:9762 import-extcommunity
#
bridge-domain 34
 vxlan vni 9843
 evpn
  route-distinguisher 78:9843
  vpn-target 0:9843 export-extcommunity
  vpn-target 0:50003 export-extcommunity
  vpn-target 0:9843 import-extcommunity
#
bridge-domain 36
 vxlan vni 9777
 evpn
  route-distinguisher 78:9777
  vpn-target 0:9777 export-extcommunity
  vpn-target 0:50003 export-extcommunity
  vpn-target 0:9777 import-extcommunity
#
bridge-domain 47
 vxlan vni 9758
 evpn
  route-distinguisher 78:9758
  vpn-target 0:9758 export-extcommunity
  vpn-target 0:50001 export-extcommunity
  vpn-target 0:9758 import-extcommunity
#
bridge-domain 50
 vxlan vni 6667
 evpn
  route-distinguisher 78:6667
  vpn-target 0:6667 export-extcommunity
  vpn-target 0:50298 export-extcommunity
  vpn-target 0:6667 import-extcommunity
#
bridge-domain 53
 vxlan vni 6451
 evpn
  route-distinguisher 78:6451
  vpn-target 0:6451 export-extcommunity
  vpn-target 0:50134 export-extcommunity
  vpn-target 0:6451 import-extcommunity
#
bridge-domain 58
 vxlan vni 9871
 evpn
  route-distinguisher 78:9871
  vpn-target 0:9871 export-extcommunity
  vpn-target 0:50017 export-extcommunity
  vpn-target 0:9871 import-extcommunity
#
bridge-domain 63
 vxlan vni 9891
 evpn
  route-distinguisher 78:9891
  vpn-target 0:9891 export-extcommunity
  vpn-target 0:50018 export-extcommunity
  vpn-target 0:9891 import-extcommunity
#
bridge-domain 64
 vxlan vni 9707
 evpn
  route-distinguisher 78:9707
  vpn-target 0:9707 export-extcommunity
  vpn-target 0:50019 export-extcommunity
  vpn-target 0:9707 import-extcommunity
#
bridge-domain 65
 vxlan vni 9907
 evpn
  route-distinguisher 78:9907
  vpn-target 0:9907 export-extcommunity
  vpn-target 0:50021 export-extcommunity
  vpn-target 0:9907 import-extcommunity
#
bridge-domain 66
 vxlan vni 9703
 evpn
  route-distinguisher 78:9703
  vpn-target 0:9703 export-extcommunity
  vpn-target 0:50023 export-extcommunity
  vpn-target 0:9703 import-extcommunity
#
bridge-domain 71
 vxlan vni 9733
 evpn
  route-distinguisher 78:9733
  vpn-target 0:9733 export-extcommunity
  vpn-target 0:50024 export-extcommunity
  vpn-target 0:9733 import-extcommunity
#
bridge-domain 72
 vxlan vni 9826
 evpn
  route-distinguisher 78:9826
  vpn-target 0:9826 export-extcommunity
  vpn-target 0:50025 export-extcommunity
  vpn-target 0:9826 import-extcommunity
#
bridge-domain 74
 vxlan vni 9878
 evpn
  route-distinguisher 78:9878
  vpn-target 0:9878 export-extcommunity
  vpn-target 0:50027 export-extcommunity
  vpn-target 0:9878 import-extcommunity
#
bridge-domain 75
 vxlan vni 9906
 evpn
  route-distinguisher 78:9906
  vpn-target 0:9906 export-extcommunity
  vpn-target 0:50028 export-extcommunity
  vpn-target 0:9906 import-extcommunity
#
bridge-domain 77
 vxlan vni 9876
 evpn
  route-distinguisher 78:9876
  vpn-target 0:9876 export-extcommunity
  vpn-target 0:50026 export-extcommunity
  vpn-target 0:9876 import-extcommunity
#
bridge-domain 83
 vxlan vni 9613
 evpn
  route-distinguisher 78:9613
  vpn-target 0:9613 export-extcommunity
  vpn-target 0:50031 export-extcommunity
  vpn-target 0:9613 import-extcommunity
#
bridge-domain 84
 vxlan vni 9636
 evpn
  route-distinguisher 78:9636
  vpn-target 0:9636 export-extcommunity
  vpn-target 0:50030 export-extcommunity
  vpn-target 0:9636 import-extcommunity
#
bridge-domain 87
 vxlan vni 9595
 evpn
  route-distinguisher 78:9595
  vpn-target 0:9595 export-extcommunity
  vpn-target 0:50032 export-extcommunity
  vpn-target 0:9595 import-extcommunity
#
bridge-domain 88
 vxlan vni 9580
 evpn
  route-distinguisher 78:9580
  vpn-target 0:9580 export-extcommunity
  vpn-target 0:50033 export-extcommunity
  vpn-target 0:9580 import-extcommunity
#
bridge-domain 103
 vxlan vni 9572
 evpn
  route-distinguisher 78:9572
  vpn-target 0:9572 export-extcommunity
  vpn-target 0:50040 export-extcommunity
  vpn-target 0:9572 import-extcommunity
#
bridge-domain 104
 vxlan vni 9562
 evpn
  route-distinguisher 78:9562
  vpn-target 0:9562 export-extcommunity
  vpn-target 0:50041 export-extcommunity
  vpn-target 0:9562 import-extcommunity
#
bridge-domain 105
 vxlan vni 9560
 evpn
  route-distinguisher 78:9560
  vpn-target 0:9560 export-extcommunity
  vpn-target 0:50042 export-extcommunity
  vpn-target 0:9560 import-extcommunity
#
bridge-domain 166
 vxlan vni 6010
 evpn
  route-distinguisher 78:6010
  vpn-target 0:6010 export-extcommunity
  vpn-target 0:50102 export-extcommunity
  vpn-target 0:6010 import-extcommunity
#
bridge-domain 169
 vxlan vni 6007
 evpn
  route-distinguisher 78:6007
  vpn-target 0:6007 export-extcommunity
  vpn-target 0:50093 export-extcommunity
  vpn-target 0:6007 import-extcommunity
#
bridge-domain 174
 vxlan vni 9596
 evpn
  route-distinguisher 78:9596
  vpn-target 0:9596 export-extcommunity
  vpn-target 0:50098 export-extcommunity
  vpn-target 0:9596 import-extcommunity
#
bridge-domain 176
 vxlan vni 6085
 evpn
  route-distinguisher 78:6085
  vpn-target 0:6085 export-extcommunity
  vpn-target 0:50105 export-extcommunity
  vpn-target 0:6085 import-extcommunity
#
bridge-domain 179
 vxlan vni 6021
 evpn
  route-distinguisher 78:6021
  vpn-target 0:6021 export-extcommunity
  vpn-target 0:50113 export-extcommunity
  vpn-target 0:6021 import-extcommunity
#
bridge-domain 198
 vxlan vni 9829
 evpn
  route-distinguisher 78:9829
  vpn-target 0:9829 export-extcommunity
  vpn-target 0:50112 export-extcommunity
  vpn-target 0:9829 import-extcommunity
#
bridge-domain 200
 vxlan vni 9608
 evpn
  route-distinguisher 78:9608
  vpn-target 0:9608 export-extcommunity
  vpn-target 0:50114 export-extcommunity
  vpn-target 0:9608 import-extcommunity
#
bridge-domain 208
 vxlan vni 9811
 evpn
  route-distinguisher 78:9811
  vpn-target 0:50059 export-extcommunity
  vpn-target 0:9811 export-extcommunity
  vpn-target 0:9811 import-extcommunity
#
bridge-domain 210
 vxlan vni 9880
 evpn
  route-distinguisher 78:9880
  vpn-target 0:9880 export-extcommunity
  vpn-target 0:50059 export-extcommunity
  vpn-target 0:9880 import-extcommunity
#
bridge-domain 221
 vxlan vni 6101
 evpn
  route-distinguisher 78:6101
  vpn-target 0:6101 export-extcommunity
  vpn-target 0:50120 export-extcommunity
  vpn-target 0:6101 import-extcommunity
#
bridge-domain 223
 vxlan vni 6108
 evpn
  route-distinguisher 78:6108
  vpn-target 0:6108 export-extcommunity
  vpn-target 0:50122 export-extcommunity
  vpn-target 0:6108 import-extcommunity
#
bridge-domain 224
 vxlan vni 6018
 evpn
  route-distinguisher 78:6018
  vpn-target 0:6018 export-extcommunity
  vpn-target 0:50123 export-extcommunity
  vpn-target 0:6018 import-extcommunity
#
bridge-domain 226
 vxlan vni 6134
 evpn
  route-distinguisher 78:6134
  vpn-target 0:6134 export-extcommunity
  vpn-target 0:50041 export-extcommunity
  vpn-target 0:6134 import-extcommunity
#
bridge-domain 262
 vxlan vni 6151
 evpn
  route-distinguisher 78:6151
  vpn-target 0:6151 export-extcommunity
  vpn-target 0:50136 export-extcommunity
  vpn-target 0:6151 import-extcommunity
#
bridge-domain 263
 vxlan vni 6169
 evpn
  route-distinguisher 78:6169
  vpn-target 0:50136 export-extcommunity
  vpn-target 0:6169 export-extcommunity
  vpn-target 0:6169 import-extcommunity
#
bridge-domain 279
 vxlan vni 6089
 evpn
  route-distinguisher 78:6089
  vpn-target 0:6089 export-extcommunity
  vpn-target 0:50139 export-extcommunity
  vpn-target 0:6089 import-extcommunity
#
bridge-domain 280
 vxlan vni 6054
 evpn
  route-distinguisher 78:6054
  vpn-target 0:6054 export-extcommunity
  vpn-target 0:50140 export-extcommunity
  vpn-target 0:6054 import-extcommunity
#
bridge-domain 281
 vxlan vni 6090
 evpn
  route-distinguisher 78:6090
  vpn-target 0:6090 export-extcommunity
  vpn-target 0:50141 export-extcommunity
  vpn-target 0:6090 import-extcommunity
#
bridge-domain 287
 vxlan vni 6053
 evpn
  route-distinguisher 78:6053
  vpn-target 0:6053 export-extcommunity
  vpn-target 0:50132 export-extcommunity
  vpn-target 0:6053 import-extcommunity
#
bridge-domain 288
 vxlan vni 6105
 evpn
  route-distinguisher 78:6105
  vpn-target 0:6105 export-extcommunity
  vpn-target 0:50042 export-extcommunity
  vpn-target 0:6105 import-extcommunity
#
bridge-domain 319
 vxlan vni 6107
 evpn
  route-distinguisher 78:6107
  vpn-target 0:6107 export-extcommunity
  vpn-target 0:50145 export-extcommunity
  vpn-target 0:6107 import-extcommunity
#
bridge-domain 337
 vxlan vni 6120
 evpn
  route-distinguisher 78:6120
  vpn-target 0:6120 export-extcommunity
  vpn-target 0:50153 export-extcommunity
  vpn-target 0:6120 import-extcommunity
#
bridge-domain 358
 vxlan vni 6206
 evpn
  route-distinguisher 78:6206
  vpn-target 0:50162 export-extcommunity
  vpn-target 0:6206 export-extcommunity
  vpn-target 0:6206 import-extcommunity
#
bridge-domain 363
 vxlan vni 6226
 evpn
  route-distinguisher 78:6226
  vpn-target 0:6226 export-extcommunity
  vpn-target 0:50165 export-extcommunity
  vpn-target 0:6226 import-extcommunity
#
bridge-domain 369
 vxlan vni 6069
 evpn
  route-distinguisher 78:6069
  vpn-target 0:6069 export-extcommunity
  vpn-target 0:50139 export-extcommunity
  vpn-target 0:6069 import-extcommunity
#
bridge-domain 373
 vxlan vni 6082
 evpn
  route-distinguisher 78:6082
  vpn-target 0:6082 export-extcommunity
  vpn-target 0:50139 export-extcommunity
  vpn-target 0:6082 import-extcommunity
#
bridge-domain 402
 vxlan vni 6200
 evpn
  route-distinguisher 78:6200
  vpn-target 0:6200 export-extcommunity
  vpn-target 0:50080 export-extcommunity
  vpn-target 0:6200 import-extcommunity
#
bridge-domain 403
 vxlan vni 6142
 evpn
  route-distinguisher 78:6142
  vpn-target 0:6142 export-extcommunity
  vpn-target 0:50172 export-extcommunity
  vpn-target 0:6142 import-extcommunity
#
bridge-domain 409
 vxlan vni 6205
 evpn
  route-distinguisher 78:6205
  vpn-target 0:50139 export-extcommunity
  vpn-target 0:6205 export-extcommunity
  vpn-target 0:6205 import-extcommunity
#
bridge-domain 413
 vxlan vni 6189
 evpn
  route-distinguisher 78:6189
  vpn-target 0:6189 export-extcommunity
  vpn-target 0:50176 export-extcommunity
  vpn-target 0:6189 import-extcommunity
#
bridge-domain 424
 vxlan vni 6247
 evpn
  route-distinguisher 78:6247
  vpn-target 0:6247 export-extcommunity
  vpn-target 0:50187 export-extcommunity
  vpn-target 0:6247 import-extcommunity
#
bridge-domain 438
 vxlan vni 6251
 evpn
  route-distinguisher 78:6251
  vpn-target 0:6251 export-extcommunity
  vpn-target 0:50140 export-extcommunity
  vpn-target 0:6251 import-extcommunity
#
bridge-domain 557
 vxlan vni 6239
 evpn
  route-distinguisher 78:6239
  vpn-target 0:50225 export-extcommunity
  vpn-target 0:6239 export-extcommunity
  vpn-target 0:6239 import-extcommunity
#
bridge-domain 609
 vxlan vni 6403
 evpn
  route-distinguisher 78:6403
  vpn-target 0:50139 export-extcommunity
  vpn-target 0:6403 export-extcommunity
  vpn-target 0:6403 import-extcommunity
#
bridge-domain 663
 vxlan vni 6423
 evpn
  route-distinguisher 78:6423
  vpn-target 0:6423 export-extcommunity
  vpn-target 0:50261 export-extcommunity
  vpn-target 0:6423 import-extcommunity
#
bridge-domain 677
 vxlan vni 6479
 evpn
  route-distinguisher 78:6479
  vpn-target 0:50269 export-extcommunity
  vpn-target 0:6479 export-extcommunity
  vpn-target 0:6479 import-extcommunity
#
bridge-domain 681
 vxlan vni 6697
 evpn
  route-distinguisher 78:6697
  vpn-target 0:50123 export-extcommunity
  vpn-target 0:6697 export-extcommunity
  vpn-target 0:6697 import-extcommunity
#
bridge-domain 682
 vxlan vni 6424
 evpn
  route-distinguisher 78:6424
  vpn-target 0:6424 export-extcommunity
  vpn-target 0:50393 export-extcommunity
  vpn-target 0:6424 import-extcommunity
#
bridge-domain 703
 vxlan vni 6499
 evpn
  route-distinguisher 78:6499
  vpn-target 0:6499 export-extcommunity
  vpn-target 0:50261 export-extcommunity
  vpn-target 0:6499 import-extcommunity
#
bridge-domain 870
 vxlan vni 6582
 evpn
  route-distinguisher 78:6582
  vpn-target 0:6582 export-extcommunity
  vpn-target 0:50176 export-extcommunity
  vpn-target 0:6582 import-extcommunity
#
bridge-domain 895
 vxlan vni 6553
 evpn
  route-distinguisher 78:6553
  vpn-target 0:6553 export-extcommunity
  vpn-target 0:50134 export-extcommunity
  vpn-target 0:6553 import-extcommunity
#
bridge-domain 934
 vxlan vni 6694
 evpn
  route-distinguisher 78:6694
  vpn-target 0:6694 export-extcommunity
  vpn-target 0:50134 export-extcommunity
  vpn-target 0:6694 import-extcommunity
#
bridge-domain 935
 vxlan vni 6422
 evpn
  route-distinguisher 78:6422
  vpn-target 0:50134 export-extcommunity
  vpn-target 0:6422 export-extcommunity
  vpn-target 0:6422 import-extcommunity
#
bridge-domain 936
 vxlan vni 6638
 evpn
  route-distinguisher 78:6638
  vpn-target 0:50134 export-extcommunity
  vpn-target 0:6638 export-extcommunity
  vpn-target 0:6638 import-extcommunity
#
bridge-domain 937
 vxlan vni 6617
 evpn
  route-distinguisher 78:6617
  vpn-target 0:6617 export-extcommunity
  vpn-target 0:50134 export-extcommunity
  vpn-target 0:6617 import-extcommunity
#
bridge-domain 952
 vxlan vni 6522
 evpn
  route-distinguisher 78:6522
  vpn-target 0:6522 export-extcommunity
  vpn-target 0:50263 export-extcommunity
  vpn-target 0:6522 import-extcommunity
#
bridge-domain 955
 vxlan vni 6645
 evpn
  route-distinguisher 78:6645
  vpn-target 0:6645 export-extcommunity
  vpn-target 0:50176 export-extcommunity
  vpn-target 0:6645 import-extcommunity
#
bridge-domain 959
 vxlan vni 6497
 evpn
  route-distinguisher 78:6497
  vpn-target 0:6497 export-extcommunity
  vpn-target 0:50261 export-extcommunity
  vpn-target 0:6497 import-extcommunity
#
bridge-domain 1040
 vxlan vni 6746
 evpn
  route-distinguisher 78:6746
  vpn-target 0:50042 export-extcommunity
  vpn-target 0:6746 export-extcommunity
  vpn-target 0:6746 import-extcommunity
#
bridge-domain 1044
 vxlan vni 6698
 evpn
  route-distinguisher 78:6698
  vpn-target 0:6698 export-extcommunity
  vpn-target 0:50261 export-extcommunity
  vpn-target 0:6698 import-extcommunity
#
bridge-domain 1050
 vxlan vni 6611
 evpn
  route-distinguisher 78:6611
  vpn-target 0:6611 export-extcommunity
  vpn-target 0:50042 export-extcommunity
  vpn-target 0:6611 import-extcommunity
#
bridge-domain 1051
 vxlan vni 6627
 evpn
  route-distinguisher 78:6627
  vpn-target 0:50134 export-extcommunity
  vpn-target 0:6627 export-extcommunity
  vpn-target 0:6627 import-extcommunity
#
bridge-domain 1052
 vxlan vni 6412
 evpn
  route-distinguisher 78:6412
  vpn-target 0:50134 export-extcommunity
  vpn-target 0:6412 export-extcommunity
  vpn-target 0:6412 import-extcommunity
#
bridge-domain 1053
 vxlan vni 6754
 evpn
  route-distinguisher 78:6754
  vpn-target 0:50134 export-extcommunity
  vpn-target 0:6754 export-extcommunity
  vpn-target 0:6754 import-extcommunity
#
bridge-domain 1054
 vxlan vni 6687
 evpn
  route-distinguisher 78:6687
  vpn-target 0:50134 export-extcommunity
  vpn-target 0:6687 export-extcommunity
  vpn-target 0:6687 import-extcommunity
#
bridge-domain 1077
 vxlan vni 6724
 evpn
  route-distinguisher 78:6724
  vpn-target 0:50134 export-extcommunity
  vpn-target 0:6724 export-extcommunity
  vpn-target 0:6724 import-extcommunity
#
bridge-domain 1078
 vxlan vni 6745
 evpn
  route-distinguisher 78:6745
  vpn-target 0:50134 export-extcommunity
  vpn-target 0:6745 export-extcommunity
  vpn-target 0:6745 import-extcommunity
#
bridge-domain 1125
 vxlan vni 6767
 evpn
  route-distinguisher 78:6767
  vpn-target 0:50141 export-extcommunity
  vpn-target 0:6767 export-extcommunity
  vpn-target 0:6767 import-extcommunity
#
bridge-domain 1138
 vxlan vni 6820
 evpn
  route-distinguisher 78:6820
  vpn-target 0:50513 export-extcommunity
  vpn-target 0:6820 export-extcommunity
  vpn-target 0:6820 import-extcommunity
#
bridge-domain 1139
 vxlan vni 6725
 evpn
  route-distinguisher 78:6725
  vpn-target 0:50513 export-extcommunity
  vpn-target 0:6725 export-extcommunity
  vpn-target 0:6725 import-extcommunity
#
bridge-domain 1143
 vxlan vni 6717
 evpn
  route-distinguisher 78:6717
  vpn-target 0:6717 export-extcommunity
  vpn-target 0:50261 export-extcommunity
  vpn-target 0:6717 import-extcommunity
#
bridge-domain 1149
 vxlan vni 6618
 evpn
  route-distinguisher 78:6618
  vpn-target 0:6618 export-extcommunity
  vpn-target 0:50262 export-extcommunity
  vpn-target 0:6618 import-extcommunity
#
bridge-domain 1157
 vxlan vni 6788
 evpn
  route-distinguisher 78:6788
  vpn-target 0:6788 export-extcommunity
  vpn-target 0:50261 export-extcommunity
  vpn-target 0:6788 import-extcommunity
#
bridge-domain 1203
 vxlan vni 6818
 evpn
  route-distinguisher 78:6818
  vpn-target 0:6818 export-extcommunity
  vpn-target 0:50098 export-extcommunity
  vpn-target 0:6818 import-extcommunity
#
bridge-domain 1217
 vxlan vni 6884
 evpn
  route-distinguisher 78:6884
  vpn-target 0:50513 export-extcommunity
  vpn-target 0:6884 export-extcommunity
  vpn-target 0:6884 import-extcommunity
#
bridge-domain 1224
 vxlan vni 6632
 evpn
  route-distinguisher 78:6632
  vpn-target 0:50513 export-extcommunity
  vpn-target 0:6632 export-extcommunity
  vpn-target 0:6632 import-extcommunity
#
bridge-domain 1225
 vxlan vni 6847
 evpn
  route-distinguisher 78:6847
  vpn-target 0:50513 export-extcommunity
  vpn-target 0:6847 export-extcommunity
  vpn-target 0:6847 import-extcommunity
#
acl number 2222
 description SNMP
 rule 5 permit source *********** ********
#
acl number 3333
 description vty
 rule 5 permit ip source ********** ***********
 rule 10 permit ip source ************* *********
 rule 15 permit ip source ************ *********
 rule 20 permit ip source ************ *********
 rule 25 permit ip source ************ *********
 rule 30 permit ip source ***********3 0
 rule 35 permit ip vpn-instance mgmt source ********** ***********
 rule 40 permit ip vpn-instance mgmt source ************* *********
 rule 45 permit ip vpn-instance mgmt source ************ *********
 rule 50 permit ip vpn-instance mgmt source ************ *********
 rule 55 permit ip vpn-instance mgmt source ************ *********
 rule 60 permit ip vpn-instance mgmt source ***********3 0
 rule 65 permit ip vpn-instance mgmt source *********** ********
 rule 70 permit ip source *********** ********
#

 authentication-scheme default
 #
 authorization-scheme default
 #
 accounting-scheme default
 #
 domain default
 #
 domain default_admin
#
stack
#
license
#
interface Vbdif33
 ip binding vpn-instance vpc-552a_0000005
 ip address *********** *************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif34
 ip binding vpn-instance vpc-a3ee_0000006
 ip address ************ *************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif36
 ip binding vpn-instance vpc-a3ee_0000006
 ip address ************ *************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif47
 ip binding vpn-instance vpc-dzyuwDMZ_0000009
 ip address *********** ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif50
 ip binding vpn-instance VPC_sichuan_APP_50298
 ip address *********** ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif53
 ip binding vpn-instance VPC_jsAIzx_DB_0000080
 ip address ************ ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif58
 ip binding vpn-instance ManageOne_0000011
 ip address *********** *************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif63
 ip binding vpn-instance VPC_wlzys_DMZ_0000012
 ipv6 enable
 ip address ********** *************
 ipv6 address 2408:8720:803:1:2:21:0:1/96
 ipv6 nd ra prefix 2408:8720:803:1:2:21::/96 0 0 no-autoconfig off-link
 mac-address 0000-5e00-0102
 ipv6 nd ra halt disable
 ipv6 nd autoconfig managed-address-flag
 ipv6 nd autoconfig other-flag
 ipv6 nd collect host enable
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif64
 ip binding vpn-instance VPC-wlzys-APP_0000013
 ipv6 enable
 ip address *********** *************
 ipv6 address 2408:8720:803:1:2:A:0:1/96
 ipv6 nd ra prefix 2408:8720:803:1:2:A::/96 0 0 no-autoconfig off-link
 mac-address 0000-5e00-0102
 ipv6 nd ra halt disable
 ipv6 nd autoconfig managed-address-flag
 ipv6 nd autoconfig other-flag
 ipv6 nd collect host enable
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif65
 ip binding vpn-instance VPC_wlzys_DB_0000014
 ip address *********** *************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif66
 ip binding vpn-instance VPC_otthzs_DB_0000015
 ip address *********** *************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif71
 ip binding vpn-instance VPC_yuntian_DB_0000016
 ip address *********** *************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif72
 ip binding vpn-instance VPC_yuntian_APP_0000017
 ip address *********** *************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif74
 ip binding vpn-instance VPC_otthzs_APP_0000019
 ipv6 enable
 ip address *********** *************
 ipv6 address 2408:81B0:A00:1:1500:6:0:1/96
 ipv6 nd ra prefix 2408:81B0:A00:1:1500:6::/96 0 0 no-autoconfig off-link
 mac-address 0000-5e00-0102
 ipv6 nd ra halt disable
 ipv6 nd autoconfig managed-address-flag
 ipv6 nd autoconfig other-flag
 ipv6 nd collect host enable
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif75
 ip binding vpn-instance VPC_otthzs_DMZ_0000020
 ipv6 enable
 ip address ********** *************
 ipv6 address 2408:8720:803:1:2:4:0:1/96
 ipv6 nd ra prefix 2408:8720:803:1:2:4::/96 0 0 no-autoconfig off-link
 mac-address 0000-5e00-0102
 ipv6 nd ra halt disable
 ipv6 nd autoconfig managed-address-flag
 ipv6 nd autoconfig other-flag
 ipv6 nd collect host enable
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif77
 ip binding vpn-instance VPC_yuntian_DMZ_0000018
 ip address ********** *************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif83
 ip binding vpn-instance VPC_lt_APP_0000023
 ip address ************ ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif84
 ip binding vpn-instance VPC_lt_DB_0000022
 ip address *********** ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif87
 ip binding vpn-instance VPC_fssyw_DB_0000024
 ip address ***********29 ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif88
 ip binding vpn-instance VPC_fssyw_DMZ_0000025
 ip address ********** ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif103
 ip binding vpn-instance VPC_wlsjs_APP_0000032
 ip address ************ ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif104
 ip binding vpn-instance VPC_osswlws_APP_0000033
 ip address ************* ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif105
 ip binding vpn-instance VPC_hxwcps_APP_0000034
 ip address ************* ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif166
 ip binding vpn-instance vpc-shanxi_0000050
 ip address *********** *************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif169
 ip binding vpn-instance vpc-gansu_0000044
 ip address *********** *************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif174
 ip binding vpn-instance VPC_wlaqs_DB_0000048
 ip address ************ ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif176
 ip binding vpn-instance vpc-qinghai_0000051
 ip address *********** *************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif179
 ip binding vpn-instance vpc-hebei_0000060
 ip address ********** *************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif198
 ip binding vpn-instance VPC_wlaqs_DMZ_0000053
 ip address ************ ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif200
 ip binding vpn-instance VPC_wlaqs_APP_0000058
 ip address ************* ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif208
 ip binding vpn-instance VPC_ossnls_APP_0000038
 ip address *********** *************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif210
 ip binding vpn-instance VPC_ossnls_APP_0000038
 ip address *********** *************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif221
 ip binding vpn-instance vpc-vdc_0000054
 ip address ************* ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif223
 ip binding vpn-instance vpc-yzys-DMZ_0000057
 ip address ************ ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif224
 ip binding vpn-instance vpc-yzys-APP_0000059
 ipv6 enable
 ip address *********** ***************
 ipv6 address 2408:81B0:A00:1:1500::1/96
 ipv6 nd ra prefix 2408:81B0:A00:1:1500::/96 0 0 no-autoconfig off-link
 mac-address 0000-5e00-0102
 ipv6 nd ra halt disable
 ipv6 nd autoconfig managed-address-flag
 ipv6 nd autoconfig other-flag
 ipv6 nd collect host enable
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif226
 ip binding vpn-instance VPC_osswlws_APP_0000033
 ipv6 enable
 ip address ************ ***************
 ipv6 address 2408:81B0:A00:1:1500:D:0:1/96
 ipv6 nd ra prefix 2408:81B0:A00:1:1500:D::/96 0 0 no-autoconfig off-link
 mac-address 0000-5e00-0102
 ipv6 nd ra halt disable
 ipv6 nd autoconfig managed-address-flag
 ipv6 nd autoconfig other-flag
 ipv6 nd collect host enable
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif262
 ip binding vpn-instance VPC_wlfzb_APP_0000066
 ip address *********** ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif263
 ip binding vpn-instance VPC_wlfzb_APP_0000066
 ip address ************ ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif279
 ip binding vpn-instance VPC_cywlwcps_APP_0000068
 ip address ************* ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif280
 ip binding vpn-instance VPC_cywlwcps_DB_0000069
 ipv6 enable
 ip address ************ ***************
 ipv6 address 2408:81B0:A00:1:1400::1/96
 ipv6 nd ra prefix 2408:81B0:A00:1:1400::/96 0 0 no-autoconfig off-link
 mac-address 0000-5e00-0102
 ipv6 nd ra halt disable
 ipv6 nd autoconfig managed-address-flag
 ipv6 nd autoconfig other-flag
 ipv6 nd collect host enable
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif281
 ip binding vpn-instance VPC_cywlwcps_DMZ_0000070
 ipv6 enable
 ip address *********** ***************
 ipv6 address 2408:8720:803:1:2:6:0:1/96
 ipv6 nd ra prefix 2408:8720:803:1:2:6::/96 0 0 no-autoconfig off-link
 mac-address 0000-5e00-0102
 ipv6 nd ra halt disable
 ipv6 nd autoconfig managed-address-flag
 ipv6 nd autoconfig other-flag
 ipv6 nd collect host enable
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif287
 ip binding vpn-instance VPC_hxwcps_DB_0000064
 ip address ************* ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif288
 ip binding vpn-instance VPC_hxwcps_APP_0000034
 ipv6 enable
 ip address ************* ***************
 ipv6 address 2408:81B0:A00:1:1500:2:0:1/96
 ipv6 nd ra prefix 2408:81B0:A00:1:1500:2::/96 0 0 no-autoconfig off-link
 mac-address 0000-5e00-0102
 ipv6 nd ra halt disable
 ipv6 nd autoconfig managed-address-flag
 ipv6 nd autoconfig other-flag
 ipv6 nd collect host enable
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif319
 ip binding vpn-instance VPC_shAIzx_APP_0000071
 ipv6 enable
 ip address *********** ***************
 ipv6 address 2408:81B0:A00:1:1500:B:0:1/96
 ipv6 nd ra prefix 2408:81B0:A00:1:1500:B::/96 0 0 no-autoconfig off-link
 mac-address 0000-5e00-0102
 ipv6 nd ra halt disable
 ipv6 nd autoconfig managed-address-flag
 ipv6 nd autoconfig other-flag
 ipv6 nd collect host enable
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif337
 ip binding vpn-instance vpc-osswlws-DB2_0000073
 ip address ************* ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif358
 ip binding vpn-instance VPC_shAIzx_DB_0000078
 ip address *********** ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif363
 ip binding vpn-instance VPC_shanxi1_0000079
 ip address *********** *************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif369
 ip binding vpn-instance VPC_cywlwcps_APP_0000068
 ip address ************ ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif373
 ip binding vpn-instance VPC_cywlwcps_APP_0000068
 ipv6 enable
 ip address *********** ***************
 ipv6 address 2408:81B0:A00:1:1500:44:0:1/96
 ipv6 nd ra prefix 2408:81B0:A00:1:1500:44::/96 0 0 no-autoconfig off-link
 mac-address 0000-5e00-0102
 ipv6 nd ra halt disable
 ipv6 nd autoconfig managed-address-flag
 ipv6 nd autoconfig other-flag
 ipv6 nd collect host enable
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif402
 ip binding vpn-instance VPC_ZY_WLW_APP_0000083
 ip address ************ ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif403
 ip binding vpn-instance VPC_neimenggu_0000084
 ip address ********** *************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif409
 ip binding vpn-instance VPC_cywlwcps_APP_0000068
 ip address ***********29 ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif413
 ip binding vpn-instance VPC_jsAIzx_APP_0000086
 ip address ***********93 ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif424
 ip binding vpn-instance VPC_NMS_JT_0000089
 ip address *********** ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif438
 ip binding vpn-instance VPC_cywlwcps_DB_0000069
 ipv6 enable
 ip address *********** ***************
 ipv6 address 2408:81B0:A00:1:1400:1:0:1/96
 ipv6 nd ra prefix 2408:81B0:A00:1:1400:1::/96 0 0 no-autoconfig off-link
 mac-address 0000-5e00-0102
 ipv6 nd ra halt disable
 ipv6 nd autoconfig managed-address-flag
 ipv6 nd autoconfig other-flag
 ipv6 nd collect host enable
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif557
 ip binding vpn-instance VPC_ex_IMS_xuanling_0000108
 ip address *********** ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif609
 ip binding vpn-instance VPC_cywlwcps_APP_0000068
 ipv6 enable
 ip address ************* ***************
 ipv6 address 2408:81B0:A00:1:1500:48:0:1/96
 ipv6 nd ra prefix 2408:81B0:A00:1:1500:48::/96 0 0 no-autoconfig off-link
 mac-address 0000-5e00-0102
 ipv6 nd ra halt disable
 ipv6 nd autoconfig managed-address-flag
 ipv6 nd autoconfig other-flag
 ipv6 nd collect host enable
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif663
 ip binding vpn-instance VPC_wlcpyfs_APP_50261
 ip address ************* ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif677
 ip binding vpn-instance VPC_wlcpyfs_ZNZX_50269
 ip address *********** ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif681
 ip binding vpn-instance vpc-yzys-APP_0000059
 ip address ************ ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif682
 ip binding vpn-instance VPC_wlcpyfs_IMS_50393
 ip address ************ ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif703
 ip binding vpn-instance VPC_wlcpyfs_APP_50261
 ip address ***********93 ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif870
 ip binding vpn-instance VPC_jsAIzx_APP_0000086
 ip address ***********29 ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif895
 ip binding vpn-instance VPC_jsAIzx_DB_0000080
 ip address ************ ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif934
 ip binding vpn-instance VPC_jsAIzx_DB_0000080
 ip address ************** ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif935
 ip binding vpn-instance VPC_jsAIzx_DB_0000080
 ip address ************ ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif936
 ip binding vpn-instance VPC_jsAIzx_DB_0000080
 ip address ************** ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif937
 ip binding vpn-instance VPC_jsAIzx_DB_0000080
 ip address ************* ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif952
 ip binding vpn-instance VPC_wlcpyfs_DMZ_50263
 ipv6 enable
 ip address ************* ***************
 ipv6 address 2408:8720:803:1:2::1/96
 ipv6 nd ra prefix 2408:8720:803:1:2::/96 0 0 no-autoconfig off-link
 mac-address 0000-5e00-0102
 ipv6 nd ra halt disable
 ipv6 nd autoconfig managed-address-flag
 ipv6 nd autoconfig other-flag
 ipv6 nd collect host enable
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif955
 ip binding vpn-instance VPC_jsAIzx_APP_0000086
 ip address *********** ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif959
 ip binding vpn-instance VPC_wlcpyfs_APP_50261
 ip address ***********29 ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif1040
 ip binding vpn-instance VPC_hxwcps_APP_0000034
 ipv6 enable
 ip address ************* ***************
 ipv6 address 2408:81B0:A00:1:1500:9:0:1/96
 ipv6 nd ra prefix 2408:81B0:A00:1:1500:9::/96 0 0 no-autoconfig off-link
 mac-address 0000-5e00-0102
 ipv6 nd ra halt disable
 ipv6 nd autoconfig managed-address-flag
 ipv6 nd autoconfig other-flag
 ipv6 nd collect host enable
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif1044
 ip binding vpn-instance VPC_wlcpyfs_APP_50261
 ipv6 enable
 ip address *********** ***************
 ipv6 address 2408:81B0:A00:1:1500:8:0:1/96
 ipv6 nd ra prefix 2408:81B0:A00:1:1500:8::/96 0 0 no-autoconfig off-link
 mac-address 0000-5e00-0102
 ipv6 nd ra halt disable
 ipv6 nd autoconfig managed-address-flag
 ipv6 nd autoconfig other-flag
 ipv6 nd collect host enable
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif1050
 ip binding vpn-instance VPC_hxwcps_APP_0000034
 ipv6 enable
 ip address ***********29 ***************
 ipv6 address 2408:81B0:A00:1:1500:4:0:1/96
 ipv6 nd ra prefix 2408:81B0:A00:1:1500:4::/96 0 0 no-autoconfig off-link
 mac-address 0000-5e00-0102
 ipv6 nd ra halt disable
 ipv6 nd autoconfig managed-address-flag
 ipv6 nd autoconfig other-flag
 ipv6 nd collect host enable
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif1051
 ip binding vpn-instance VPC_jsAIzx_DB_0000080
 ip address ************** ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif1052
 ip binding vpn-instance VPC_jsAIzx_DB_0000080
 ip address ************ *************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif1053
 ip binding vpn-instance VPC_jsAIzx_DB_0000080
 ip address ************ *************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif1054
 ip binding vpn-instance VPC_jsAIzx_DB_0000080
 ip address ************** ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif1077
 ip binding vpn-instance VPC_jsAIzx_DB_0000080
 ip address ************ *************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif1078
 ip binding vpn-instance VPC_jsAIzx_DB_0000080
 ip address ************ *************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif1125
 ip binding vpn-instance VPC_cywlwcps_DMZ_0000070
 ipv6 enable
 ip address ************** ***************
 ipv6 address 2408:8720:803:1:2:C:0:1/96
 ipv6 nd ra prefix 2408:8720:803:1:2:C::/96 0 0 no-autoconfig off-link
 mac-address 0000-5e00-0102
 ipv6 nd ra halt disable
 ipv6 nd autoconfig managed-address-flag
 ipv6 nd autoconfig other-flag
 ipv6 nd collect host enable
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif1138
 ip binding vpn-instance VPC_jsAIzx_DB1_50513
 ip address ************ *************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif1139
 ip binding vpn-instance VPC_jsAIzx_DB1_50513
 ip address ************ *************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif1143
 ip binding vpn-instance VPC_wlcpyfs_APP_50261
 ipv6 enable
 ip address ************** ***************
 ipv6 address 2408:81B0:A00:1:1500:10:0:1/96
 ipv6 nd ra prefix 2408:81B0:A00:1:1500:10::/96 0 0 no-autoconfig off-link
 mac-address 0000-5e00-0102
 ipv6 nd ra halt disable
 ipv6 nd autoconfig managed-address-flag
 ipv6 nd autoconfig other-flag
 ipv6 nd collect host enable
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif1149
 ip binding vpn-instance VPC_wlcpyfs_DB_50262
 ip address ************** ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif1157
 ip binding vpn-instance VPC_wlcpyfs_APP_50261
 ip address ************ ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif1203
 ip binding vpn-instance VPC_wlaqs_DB_0000048
 ip address ************ ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif1217
 ip binding vpn-instance VPC_jsAIzx_DB1_50513
 ip address ************ *************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif1224
 ip binding vpn-instance VPC_jsAIzx_DB1_50513
 ip address ************ ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface Vbdif1225
 ip binding vpn-instance VPC_jsAIzx_DB1_50513
 ip address ************ ***************
 mac-address 0000-5e00-0102
 vxlan anycast-gateway enable
 arp collect host enable
#
interface MEth0/0/0
 ip binding vpn-instance mgmt
 ip address *********** *************
#
interface Eth-Trunk0
 description "To-[ZY1B3F1-05P12L44U-Pub-HW6855-YWLF01]-eth-trunk0"
 mode lacp-static
 peer-link 1
#
interface Eth-Trunk11
 undo portswitch
 description To-[ZY1B3F1-06P15L03U-Pub-HW12816-YWSP01]-eth-trunk95
 ip address *********** ***************
 ospf peer hold-max-cost timer 800000
 mode lacp-static
#
interface Eth-Trunk12
 undo portswitch
 description To-[ZY1B3F1-10P15L03U-Pub-HW12816-YWSP02]-eth-trunk95
 ip address *********** ***************
 ospf peer hold-max-cost timer 800000
 mode lacp-static
#
interface Eth-Trunk13
 undo portswitch
 description To-[ZY1B3F2-02P15L03U-Pub-HW12816-YWSP03]-eth-trunk95
 ip address *********** ***************
 ospf peer hold-max-cost timer 800000
 mode lacp-static
#
interface Eth-Trunk14
 undo portswitch
 description To-[ZY1B3F2-06P15L03U-Pub-HW12816-YWSP04]-eth-trunk95
 ip address *********** ***************
 ospf peer hold-max-cost timer 800000
 mode lacp-static
#
interface Eth-Trunk61
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 61
 port nvo3 mode access
#
interface Eth-Trunk61.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk61.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 208
#
interface Eth-Trunk61.3 mode l2
 encapsulation dot1q vid 101
 bridge-domain 71
#
interface Eth-Trunk61.4 mode l2
 encapsulation dot1q vid 102
 bridge-domain 210
#
interface Eth-Trunk61.7 mode l2
 encapsulation dot1q vid 105
 bridge-domain 104
#
interface Eth-Trunk61.8 mode l2
 encapsulation dot1q vid 106
 bridge-domain 103
#
interface Eth-Trunk61.9 mode l2
 encapsulation dot1q vid 107
 bridge-domain 1053
#
interface Eth-Trunk61.10 mode l2
 encapsulation dot1q vid 108
 bridge-domain 1052
#
interface Eth-Trunk61.15 mode l2
 encapsulation dot1q vid 113
 bridge-domain 288
#
interface Eth-Trunk62
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 62
 port nvo3 mode access
#
interface Eth-Trunk62.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk62.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 74
#
interface Eth-Trunk62.3 mode l2
 encapsulation dot1q vid 101
 bridge-domain 1217
#
interface Eth-Trunk62.4 mode l2
 encapsulation dot1q vid 102
 bridge-domain 1224
#
interface Eth-Trunk62.9 mode l2
 encapsulation dot1q vid 107
 bridge-domain 71
#
interface Eth-Trunk62.12 mode l2
 encapsulation dot1q vid 110
 bridge-domain 64
#
interface Eth-Trunk62.13 mode l2
 encapsulation dot1q vid 111
 bridge-domain 83
#
interface Eth-Trunk62.14 mode l2
 encapsulation dot1q vid 112
 bridge-domain 63
#
interface Eth-Trunk63
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 63
 port nvo3 mode access
#
interface Eth-Trunk63.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk63.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 1053
#
interface Eth-Trunk63.3 mode l2
 encapsulation dot1q vid 101
 bridge-domain 208
#
interface Eth-Trunk63.4 mode l2
 encapsulation dot1q vid 102
 bridge-domain 1052
#
interface Eth-Trunk63.5 mode l2
 encapsulation dot1q vid 103
 bridge-domain 210
#
interface Eth-Trunk63.11 mode l2
 encapsulation dot1q vid 109
 bridge-domain 200
#
interface Eth-Trunk63.12 mode l2
 encapsulation dot1q vid 110
 bridge-domain 103
#
interface Eth-Trunk63.13 mode l2
 encapsulation dot1q vid 111
 bridge-domain 74
#
interface Eth-Trunk63.14 mode l2
 encapsulation dot1q vid 112
 bridge-domain 221
#
interface Eth-Trunk64
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 64
 port nvo3 mode access
#
interface Eth-Trunk64.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk64.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 409
#
interface Eth-Trunk64.3 mode l2
 encapsulation dot1q vid 101
 bridge-domain 287
#
interface Eth-Trunk64.4 mode l2
 encapsulation dot1q vid 102
 bridge-domain 104
#
interface Eth-Trunk64.5 mode l2
 encapsulation dot1q vid 103
 bridge-domain 1054
#
interface Eth-Trunk64.6 mode l2
 encapsulation dot1q vid 104
 bridge-domain 1051
#
interface Eth-Trunk64.7 mode l2
 encapsulation dot1q vid 105
 bridge-domain 1052
#
interface Eth-Trunk64.8 mode l2
 encapsulation dot1q vid 106
 bridge-domain 413
#
interface Eth-Trunk64.9 mode l2
 encapsulation dot1q vid 107
 bridge-domain 952
#
interface Eth-Trunk64.10 mode l2
 encapsulation dot1q vid 108
 bridge-domain 373
#
interface Eth-Trunk64.11 mode l2
 encapsulation dot1q vid 109
 bridge-domain 369
#
interface Eth-Trunk64.12 mode l2
 encapsulation dot1q vid 110
 bridge-domain 279
#
interface Eth-Trunk65
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 65
 port nvo3 mode access
#
interface Eth-Trunk65.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk65.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 1157
#
interface Eth-Trunk65.3 mode l2
 encapsulation dot1q vid 101
 bridge-domain 1143
#
interface Eth-Trunk65.5 mode l2
 encapsulation dot1q vid 103
 bridge-domain 71
#
interface Eth-Trunk65.6 mode l2
 encapsulation dot1q vid 104
 bridge-domain 50
#
interface Eth-Trunk65.7 mode l2
 encapsulation dot1q vid 105
 bridge-domain 1053
#
interface Eth-Trunk65.8 mode l2
 encapsulation dot1q vid 106
 bridge-domain 1052
#
interface Eth-Trunk65.9 mode l2
 encapsulation dot1q vid 107
 bridge-domain 373
#
interface Eth-Trunk65.10 mode l2
 encapsulation dot1q vid 108
 bridge-domain 63
#
interface Eth-Trunk65.11 mode l2
 encapsulation dot1q vid 109
 bridge-domain 682
#
interface Eth-Trunk65.12 mode l2
 encapsulation dot1q vid 110
 bridge-domain 703
#
interface Eth-Trunk65.13 mode l2
 encapsulation dot1q vid 111
 bridge-domain 663
#
interface Eth-Trunk65.14 mode l2
 encapsulation dot1q vid 112
 bridge-domain 319
#
interface Eth-Trunk65.15 mode l2
 encapsulation dot1q vid 113
 bridge-domain 402
#
interface Eth-Trunk65.16 mode l2
 encapsulation dot1q vid 114
 bridge-domain 208
#
interface Eth-Trunk65.17 mode l2
 encapsulation dot1q vid 115
 bridge-domain 557
#
interface Eth-Trunk65.18 mode l2
 encapsulation dot1q vid 116
 bridge-domain 609
#
interface Eth-Trunk66
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 66
 port nvo3 mode access
#
interface Eth-Trunk66.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk66.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 71
#
interface Eth-Trunk66.3 mode l2
 encapsulation dot1q vid 101
 bridge-domain 1217
#
interface Eth-Trunk66.4 mode l2
 encapsulation dot1q vid 102
 bridge-domain 1224
#
interface Eth-Trunk66.5 mode l2
 encapsulation dot1q vid 103
 bridge-domain 1225
#
interface Eth-Trunk66.9 mode l2
 encapsulation dot1q vid 107
 bridge-domain 1053
#
interface Eth-Trunk66.10 mode l2
 encapsulation dot1q vid 108
 bridge-domain 1052
#
interface Eth-Trunk66.11 mode l2
 encapsulation dot1q vid 109
 bridge-domain 77
#
interface Eth-Trunk66.12 mode l2
 encapsulation dot1q vid 110
 bridge-domain 33
#
interface Eth-Trunk66.13 mode l2
 encapsulation dot1q vid 111
 bridge-domain 58
#
interface Eth-Trunk67
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 67
 port nvo3 mode access
#
interface Eth-Trunk67.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk67.3 mode l2
 encapsulation dot1q vid 101
 bridge-domain 71
#
interface Eth-Trunk67.7 mode l2
 encapsulation dot1q vid 105
 bridge-domain 409
#
interface Eth-Trunk67.8 mode l2
 encapsulation dot1q vid 106
 bridge-domain 955
#
interface Eth-Trunk67.9 mode l2
 encapsulation dot1q vid 107
 bridge-domain 1052
#
interface Eth-Trunk67.10 mode l2
 encapsulation dot1q vid 108
 bridge-domain 1053
#
interface Eth-Trunk67.11 mode l2
 encapsulation dot1q vid 109
 bridge-domain 952
#
interface Eth-Trunk67.13 mode l2
 encapsulation dot1q vid 111
 bridge-domain 1050
#
interface Eth-Trunk68
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 68
 port nvo3 mode access
#
interface Eth-Trunk68.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk68.3 mode l2
 encapsulation dot1q vid 101
 bridge-domain 262
#
interface Eth-Trunk68.5 mode l2
 encapsulation dot1q vid 103
 bridge-domain 263
#
interface Eth-Trunk68.8 mode l2
 encapsulation dot1q vid 106
 bridge-domain 87
#
interface Eth-Trunk69
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 69
 port nvo3 mode access
#
interface Eth-Trunk69.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk69.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 71
#
interface Eth-Trunk69.3 mode l2
 encapsulation dot1q vid 101
 bridge-domain 358
#
interface Eth-Trunk69.5 mode l2
 encapsulation dot1q vid 103
 bridge-domain 936
#
interface Eth-Trunk69.6 mode l2
 encapsulation dot1q vid 104
 bridge-domain 363
#
interface Eth-Trunk69.7 mode l2
 encapsulation dot1q vid 105
 bridge-domain 934
#
interface Eth-Trunk69.8 mode l2
 encapsulation dot1q vid 106
 bridge-domain 935
#
interface Eth-Trunk69.9 mode l2
 encapsulation dot1q vid 107
 bridge-domain 176
#
interface Eth-Trunk69.10 mode l2
 encapsulation dot1q vid 108
 bridge-domain 223
#
interface Eth-Trunk70
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 70
 port nvo3 mode access
#
interface Eth-Trunk70.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk70.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 71
#
interface Eth-Trunk70.3 mode l2
 encapsulation dot1q vid 101
 bridge-domain 1044
#
interface Eth-Trunk70.4 mode l2
 encapsulation dot1q vid 102
 bridge-domain 210
#
interface Eth-Trunk70.5 mode l2
 encapsulation dot1q vid 103
 bridge-domain 208
#
interface Eth-Trunk70.6 mode l2
 encapsulation dot1q vid 104
 bridge-domain 84
#
interface Eth-Trunk70.7 mode l2
 encapsulation dot1q vid 105
 bridge-domain 413
#
interface Eth-Trunk70.8 mode l2
 encapsulation dot1q vid 106
 bridge-domain 72
#
interface Eth-Trunk71
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 71
 port nvo3 mode access
#
interface Eth-Trunk71.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk71.2 mode l2
 encapsulation dot1q vid 107
 bridge-domain 703
#
interface Eth-Trunk71.3 mode l2
 encapsulation dot1q vid 105
 bridge-domain 1052
#
interface Eth-Trunk71.4 mode l2
 encapsulation dot1q vid 104
 bridge-domain 1053
#
interface Eth-Trunk71.5 mode l2
 encapsulation dot1q vid 112
 bridge-domain 1054
#
interface Eth-Trunk71.6 mode l2
 encapsulation dot1q vid 113
 bridge-domain 409
#
interface Eth-Trunk71.7 mode l2
 encapsulation dot1q vid 103
 bridge-domain 208
#
interface Eth-Trunk71.8 mode l2
 encapsulation dot1q vid 102
 bridge-domain 210
#
interface Eth-Trunk71.9 mode l2
 encapsulation dot1q vid 100
 bridge-domain 71
#
interface Eth-Trunk71.10 mode l2
 encapsulation dot1q vid 114
 bridge-domain 895
#
interface Eth-Trunk71.11 mode l2
 encapsulation dot1q vid 115
 bridge-domain 53
#
interface Eth-Trunk72
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 72
 port nvo3 mode access
#
interface Eth-Trunk72.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk72.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 281
#
interface Eth-Trunk72.3 mode l2
 encapsulation dot1q vid 101
 bridge-domain 1143
#
interface Eth-Trunk72.4 mode l2
 encapsulation dot1q vid 102
 bridge-domain 1157
#
interface Eth-Trunk72.5 mode l2
 encapsulation dot1q vid 103
 bridge-domain 424
#
interface Eth-Trunk72.7 mode l2
 encapsulation dot1q vid 105
 bridge-domain 681
#
interface Eth-Trunk72.9 mode l2
 encapsulation dot1q vid 107
 bridge-domain 64
#
interface Eth-Trunk73
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 73
 port nvo3 mode access
#
interface Eth-Trunk73.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk73.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 223
#
interface Eth-Trunk73.3 mode l2
 encapsulation dot1q vid 101
 bridge-domain 74
#
interface Eth-Trunk73.4 mode l2
 encapsulation dot1q vid 102
 bridge-domain 319
#
interface Eth-Trunk73.5 mode l2
 encapsulation dot1q vid 103
 bridge-domain 363
#
interface Eth-Trunk73.6 mode l2
 encapsulation dot1q vid 105
 bridge-domain 337
#
interface Eth-Trunk73.7 mode l2
 encapsulation dot1q vid 104
 bridge-domain 224
#
interface Eth-Trunk73.8 mode l2
 encapsulation dot1q vid 106
 bridge-domain 33
#
interface Eth-Trunk73.9 mode l2
 encapsulation dot1q vid 108
 bridge-domain 169
#
interface Eth-Trunk74
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 74
 port nvo3 mode access
#
interface Eth-Trunk74.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk74.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 169
#
interface Eth-Trunk74.7 mode l2
 encapsulation dot1q vid 105
 bridge-domain 409
#
interface Eth-Trunk74.11 mode l2
 encapsulation dot1q vid 109
 bridge-domain 104
#
interface Eth-Trunk75
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 75
 port nvo3 mode access
#
interface Eth-Trunk75.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk75.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 58
#
interface Eth-Trunk75.7 mode l2
 encapsulation dot1q vid 105
 bridge-domain 71
#
interface Eth-Trunk75.8 mode l2
 encapsulation dot1q vid 106
 bridge-domain 224
#
interface Eth-Trunk75.9 mode l2
 encapsulation dot1q vid 107
 bridge-domain 176
#
interface Eth-Trunk76
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 76
 port nvo3 mode access
#
interface Eth-Trunk76.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk76.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 87
#
interface Eth-Trunk76.3 mode l2
 encapsulation dot1q vid 101
 bridge-domain 103
#
interface Eth-Trunk76.4 mode l2
 encapsulation dot1q vid 102
 bridge-domain 72
#
interface Eth-Trunk76.7 mode l2
 encapsulation dot1q vid 105
 bridge-domain 934
#
interface Eth-Trunk76.8 mode l2
 encapsulation dot1q vid 106
 bridge-domain 936
#
interface Eth-Trunk76.9 mode l2
 encapsulation dot1q vid 107
 bridge-domain 935
#
interface Eth-Trunk76.10 mode l2
 encapsulation dot1q vid 108
 bridge-domain 937
#
interface Eth-Trunk76.11 mode l2
 encapsulation dot1q vid 109
 bridge-domain 75
#
interface Eth-Trunk76.12 mode l2
 encapsulation dot1q vid 110
 bridge-domain 409
#
interface Eth-Trunk77
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 77
 port nvo3 mode access
#
interface Eth-Trunk77.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk77.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 208
#
interface Eth-Trunk77.3 mode l2
 encapsulation dot1q vid 101
 bridge-domain 210
#
interface Eth-Trunk77.4 mode l2
 encapsulation dot1q vid 102
 bridge-domain 74
#
interface Eth-Trunk77.5 mode l2
 encapsulation dot1q vid 103
 bridge-domain 66
#
interface Eth-Trunk77.7 mode l2
 encapsulation dot1q vid 105
 bridge-domain 263
#
interface Eth-Trunk77.8 mode l2
 encapsulation dot1q vid 106
 bridge-domain 262
#
interface Eth-Trunk78
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 78
 port nvo3 mode access
#
interface Eth-Trunk78.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk78.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 169
#
interface Eth-Trunk78.3 mode l2
 encapsulation dot1q vid 101
 bridge-domain 1050
#
interface Eth-Trunk78.4 mode l2
 encapsulation dot1q vid 102
 bridge-domain 208
#
interface Eth-Trunk78.5 mode l2
 encapsulation dot1q vid 103
 bridge-domain 210
#
interface Eth-Trunk78.6 mode l2
 encapsulation dot1q vid 104
 bridge-domain 409
#
interface Eth-Trunk78.7 mode l2
 encapsulation dot1q vid 105
 bridge-domain 103
#
interface Eth-Trunk78.8 mode l2
 encapsulation dot1q vid 106
 bridge-domain 1052
#
interface Eth-Trunk78.9 mode l2
 encapsulation dot1q vid 107
 bridge-domain 1053
#
interface Eth-Trunk79
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 79
 port nvo3 mode access
#
interface Eth-Trunk79.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk79.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 1044
#
interface Eth-Trunk79.3 mode l2
 encapsulation dot1q vid 101
 bridge-domain 280
#
interface Eth-Trunk79.4 mode l2
 encapsulation dot1q vid 102
 bridge-domain 66
#
interface Eth-Trunk79.5 mode l2
 encapsulation dot1q vid 103
 bridge-domain 1052
#
interface Eth-Trunk79.6 mode l2
 encapsulation dot1q vid 104
 bridge-domain 1051
#
interface Eth-Trunk79.7 mode l2
 encapsulation dot1q vid 105
 bridge-domain 63
#
interface Eth-Trunk79.8 mode l2
 encapsulation dot1q vid 106
 bridge-domain 64
#
interface Eth-Trunk79.10 mode l2
 encapsulation dot1q vid 108
 bridge-domain 677
#
interface Eth-Trunk79.12 mode l2
 encapsulation dot1q vid 110
 bridge-domain 74
#
interface Eth-Trunk79.14 mode l2
 encapsulation dot1q vid 112
 bridge-domain 870
#
interface Eth-Trunk79.15 mode l2
 encapsulation dot1q vid 113
 bridge-domain 224
#
interface Eth-Trunk79.16 mode l2
 encapsulation dot1q vid 114
 bridge-domain 413
#
interface Eth-Trunk80
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 80
 port nvo3 mode access
#
interface Eth-Trunk80.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk80.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 71
#
interface Eth-Trunk80.3 mode l2
 encapsulation dot1q vid 101
 bridge-domain 1050
#
interface Eth-Trunk80.8 mode l2
 encapsulation dot1q vid 106
 bridge-domain 210
#
interface Eth-Trunk80.9 mode l2
 encapsulation dot1q vid 107
 bridge-domain 208
#
interface Eth-Trunk81
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 81
 port nvo3 mode access
#
interface Eth-Trunk81.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk81.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 1050
#
interface Eth-Trunk81.7 mode l2
 encapsulation dot1q vid 105
 bridge-domain 403
#
interface Eth-Trunk81.8 mode l2
 encapsulation dot1q vid 106
 bridge-domain 65
#
interface Eth-Trunk81.9 mode l2
 encapsulation dot1q vid 107
 bridge-domain 952
#
interface Eth-Trunk82
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 82
 port nvo3 mode access
#
interface Eth-Trunk82.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk82.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 287
#
interface Eth-Trunk82.8 mode l2
 encapsulation dot1q vid 107
 bridge-domain 226
#
interface Eth-Trunk82.10 mode l2
 encapsulation dot1q vid 108
 bridge-domain 438
#
interface Eth-Trunk82.11 mode l2
 encapsulation dot1q vid 109
 bridge-domain 959
#
interface Eth-Trunk82.12 mode l2
 encapsulation dot1q vid 110
 bridge-domain 1052
#
interface Eth-Trunk82.13 mode l2
 encapsulation dot1q vid 111
 bridge-domain 1053
#
interface Eth-Trunk82.15 mode l2
 encapsulation dot1q vid 113
 bridge-domain 363
#
interface Eth-Trunk82.16 mode l2
 encapsulation dot1q vid 114
 bridge-domain 83
#
interface Eth-Trunk82.17 mode l2
 encapsulation dot1q vid 115
 bridge-domain 413
#
interface Eth-Trunk83
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 83
 port nvo3 mode access
#
interface Eth-Trunk83.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk83.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 287
#
interface Eth-Trunk83.3 mode l2
 encapsulation dot1q vid 101
 bridge-domain 424
#
interface Eth-Trunk83.4 mode l2
 encapsulation dot1q vid 102
 bridge-domain 36
#
interface Eth-Trunk83.5 mode l2
 encapsulation dot1q vid 103
 bridge-domain 34
#
interface Eth-Trunk83.6 mode l2
 encapsulation dot1q vid 104
 bridge-domain 1053
#
interface Eth-Trunk83.7 mode l2
 encapsulation dot1q vid 105
 bridge-domain 1052
#
interface Eth-Trunk83.8 mode l2
 encapsulation dot1q vid 106
 bridge-domain 104
#
interface Eth-Trunk84
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 84
 port nvo3 mode access
#
interface Eth-Trunk84.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk84.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 287
#
interface Eth-Trunk84.3 mode l2
 encapsulation dot1q vid 101
 bridge-domain 58
#
interface Eth-Trunk84.4 mode l2
 encapsulation dot1q vid 102
 bridge-domain 1044
#
interface Eth-Trunk84.5 mode l2
 encapsulation dot1q vid 103
 bridge-domain 169
#
interface Eth-Trunk84.6 mode l2
 encapsulation dot1q vid 104
 bridge-domain 279
#
interface Eth-Trunk85
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 85
 port nvo3 mode access
#
interface Eth-Trunk85.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk85.3 mode l2
 encapsulation dot1q vid 101
 bridge-domain 363
#
interface Eth-Trunk85.4 mode l2
 encapsulation dot1q vid 102
 bridge-domain 105
#
interface Eth-Trunk85.5 mode l2
 encapsulation dot1q vid 103
 bridge-domain 71
#
interface Eth-Trunk86
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 86
 port nvo3 mode access
#
interface Eth-Trunk86.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk86.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 1149
#
interface Eth-Trunk86.5 mode l2
 encapsulation dot1q vid 102
 bridge-domain 224
#
interface Eth-Trunk86.6 mode l2
 encapsulation dot1q vid 103
 bridge-domain 1053
#
interface Eth-Trunk86.7 mode l2
 encapsulation dot1q vid 104
 bridge-domain 1052
#
interface Eth-Trunk86.8 mode l2
 encapsulation dot1q vid 105
 bridge-domain 438
#
interface Eth-Trunk86.9 mode l2
 encapsulation dot1q vid 106
 bridge-domain 83
#
interface Eth-Trunk86.10 mode l2
 encapsulation dot1q vid 107
 bridge-domain 103
#
interface Eth-Trunk87
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 87
 port nvo3 mode access
#
interface Eth-Trunk87.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk88
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 88
 port nvo3 mode access
#
interface Eth-Trunk88.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk88.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 409
#
interface Eth-Trunk88.3 mode l2
 encapsulation dot1q vid 101
 bridge-domain 403
#
interface Eth-Trunk88.4 mode l2
 encapsulation dot1q vid 102
 bridge-domain 58
#
interface Eth-Trunk88.5 mode l2
 encapsulation dot1q vid 103
 bridge-domain 200
#
interface Eth-Trunk88.6 mode l2
 encapsulation dot1q vid 104
 bridge-domain 210
#
interface Eth-Trunk88.7 mode l2
 encapsulation dot1q vid 105
 bridge-domain 208
#
interface Eth-Trunk88.9 mode l2
 encapsulation dot1q vid 107
 bridge-domain 103
#
interface Eth-Trunk88.10 mode l2
 encapsulation dot1q vid 108
 bridge-domain 1052
#
interface Eth-Trunk88.11 mode l2
 encapsulation dot1q vid 109
 bridge-domain 1053
#
interface Eth-Trunk88.12 mode l2
 encapsulation dot1q vid 110
 bridge-domain 1054
#
interface Eth-Trunk88.13 mode l2
 encapsulation dot1q vid 111
 bridge-domain 413
#
interface Eth-Trunk89
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 89
 port nvo3 mode access
#
interface Eth-Trunk89.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk89.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 1050
#
interface Eth-Trunk89.3 mode l2
 encapsulation dot1q vid 101
 bridge-domain 403
#
interface Eth-Trunk89.4 mode l2
 encapsulation dot1q vid 102
 bridge-domain 210
#
interface Eth-Trunk89.5 mode l2
 encapsulation dot1q vid 103
 bridge-domain 208
#
interface Eth-Trunk89.6 mode l2
 encapsulation dot1q vid 105
 bridge-domain 224
#
interface Eth-Trunk89.7 mode l2
 encapsulation dot1q vid 106
 bridge-domain 1052
#
interface Eth-Trunk89.8 mode l2
 encapsulation dot1q vid 107
 bridge-domain 1053
#
interface Eth-Trunk89.10 mode l2
 encapsulation dot1q vid 109
 bridge-domain 955
#
interface Eth-Trunk90
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 90
 port nvo3 mode access
#
interface Eth-Trunk90.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk90.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 176
#
interface Eth-Trunk90.3 mode l2
 encapsulation dot1q vid 101
 bridge-domain 34
#
interface Eth-Trunk90.4 mode l2
 encapsulation dot1q vid 102
 bridge-domain 36
#
interface Eth-Trunk90.5 mode l2
 encapsulation dot1q vid 103
 bridge-domain 198
#
interface Eth-Trunk90.6 mode l2
 encapsulation dot1q vid 104
 bridge-domain 200
#
interface Eth-Trunk90.7 mode l2
 encapsulation dot1q vid 105
 bridge-domain 71
#
interface Eth-Trunk90.8 mode l2
 encapsulation dot1q vid 106
 bridge-domain 337
#
interface Eth-Trunk90.9 mode l2
 encapsulation dot1q vid 107
 bridge-domain 936
#
interface Eth-Trunk90.10 mode l2
 encapsulation dot1q vid 108
 bridge-domain 934
#
interface Eth-Trunk90.11 mode l2
 encapsulation dot1q vid 109
 bridge-domain 935
#
interface Eth-Trunk91
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 91
 port nvo3 mode access
#
interface Eth-Trunk91.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk91.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 1052
#
interface Eth-Trunk91.3 mode l2
 encapsulation dot1q vid 101
 bridge-domain 36
#
interface Eth-Trunk91.4 mode l2
 encapsulation dot1q vid 102
 bridge-domain 34
#
interface Eth-Trunk91.5 mode l2
 encapsulation dot1q vid 103
 bridge-domain 1053
#
interface Eth-Trunk91.6 mode l2
 encapsulation dot1q vid 104
 bridge-domain 1143
#
interface Eth-Trunk91.7 mode l2
 encapsulation dot1q vid 105
 bridge-domain 1157
#
interface Eth-Trunk91.8 mode l2
 encapsulation dot1q vid 106
 bridge-domain 71
#
interface Eth-Trunk92
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 92
 port nvo3 mode access
#
interface Eth-Trunk92.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk92.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 262
#
interface Eth-Trunk92.3 mode l2
 encapsulation dot1q vid 101
 bridge-domain 263
#
interface Eth-Trunk92.4 mode l2
 encapsulation dot1q vid 102
 bridge-domain 224
#
interface Eth-Trunk92.5 mode l2
 encapsulation dot1q vid 103
 bridge-domain 959
#
interface Eth-Trunk92.6 mode l2
 encapsulation dot1q vid 104
 bridge-domain 179
#
interface Eth-Trunk92.7 mode l2
 encapsulation dot1q vid 105
 bridge-domain 88
#
interface Eth-Trunk92.8 mode l2
 encapsulation dot1q vid 106
 bridge-domain 409
#
interface Eth-Trunk92.9 mode l2
 encapsulation dot1q vid 107
 bridge-domain 208
#
interface Eth-Trunk92.10 mode l2
 encapsulation dot1q vid 108
 bridge-domain 210
#
interface Eth-Trunk92.11 mode l2
 encapsulation dot1q vid 109
 bridge-domain 71
#
interface Eth-Trunk92.19 mode l2
 encapsulation dot1q vid 117
 bridge-domain 226
#
interface Eth-Trunk93
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 93
 port nvo3 mode access
#
interface Eth-Trunk93.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk93.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 71
#
interface Eth-Trunk93.3 mode l2
 encapsulation dot1q vid 101
 bridge-domain 1050
#
interface Eth-Trunk93.4 mode l2
 encapsulation dot1q vid 102
 bridge-domain 363
#
interface Eth-Trunk93.5 mode l2
 encapsulation dot1q vid 103
 bridge-domain 104
#
interface Eth-Trunk93.6 mode l2
 encapsulation dot1q vid 104
 bridge-domain 959
#
interface Eth-Trunk93.7 mode l2
 encapsulation dot1q vid 105
 bridge-domain 413
#
interface Eth-Trunk93.8 mode l2
 encapsulation dot1q vid 106
 bridge-domain 1053
#
interface Eth-Trunk93.9 mode l2
 encapsulation dot1q vid 107
 bridge-domain 1052
#
interface Eth-Trunk93.10 mode l2
 encapsulation dot1q vid 108
 bridge-domain 1054
#
interface Eth-Trunk94
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 94
 port nvo3 mode access
#
interface Eth-Trunk94.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk94.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 1138
#
interface Eth-Trunk94.3 mode l2
 encapsulation dot1q vid 101
 bridge-domain 87
#
interface Eth-Trunk94.4 mode l2
 encapsulation dot1q vid 102
 bridge-domain 176
#
interface Eth-Trunk94.5 mode l2
 encapsulation dot1q vid 103
 bridge-domain 1139
#
interface Eth-Trunk94.6 mode l2
 encapsulation dot1q vid 104
 bridge-domain 935
#
interface Eth-Trunk94.7 mode l2
 encapsulation dot1q vid 105
 bridge-domain 934
#
interface Eth-Trunk94.8 mode l2
 encapsulation dot1q vid 106
 bridge-domain 936
#
interface Eth-Trunk94.9 mode l2
 encapsulation dot1q vid 107
 bridge-domain 952
#
interface Eth-Trunk94.10 mode l2
 encapsulation dot1q vid 108
 bridge-domain 1157
#
interface Eth-Trunk94.11 mode l2
 encapsulation dot1q vid 109
 bridge-domain 1143
#
interface Eth-Trunk95
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 95
 port nvo3 mode access
#
interface Eth-Trunk95.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk95.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 424
#
interface Eth-Trunk95.3 mode l2
 encapsulation dot1q vid 101
 bridge-domain 1149
#
interface Eth-Trunk95.6 mode l2
 encapsulation dot1q vid 108
 bridge-domain 895
#
interface Eth-Trunk95.7 mode l2
 encapsulation dot1q vid 112
 bridge-domain 53
#
interface Eth-Trunk95.11 mode l2
 encapsulation dot1q vid 109
 bridge-domain 263
#
interface Eth-Trunk95.12 mode l2
 encapsulation dot1q vid 110
 bridge-domain 262
#
interface Eth-Trunk95.13 mode l2
 encapsulation dot1q vid 111
 bridge-domain 103
#
interface Eth-Trunk95.15 mode l2
 encapsulation dot1q vid 113
 bridge-domain 166
#
interface Eth-Trunk95.16 mode l2
 encapsulation dot1q vid 114
 bridge-domain 72
#
interface Eth-Trunk96
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 96
 port nvo3 mode access
#
interface Eth-Trunk96.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk96.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 934
#
interface Eth-Trunk96.4 mode l2
 encapsulation dot1q vid 102
 bridge-domain 935
#
interface Eth-Trunk96.6 mode l2
 encapsulation dot1q vid 103
 bridge-domain 936
#
interface Eth-Trunk96.8 mode l2
 encapsulation dot1q vid 106
 bridge-domain 280
#
interface Eth-Trunk96.10 mode l2
 encapsulation dot1q vid 108
 bridge-domain 1052
#
interface Eth-Trunk96.11 mode l2
 encapsulation dot1q vid 109
 bridge-domain 1053
#
interface Eth-Trunk96.12 mode l2
 encapsulation dot1q vid 110
 bridge-domain 166
#
interface Eth-Trunk97
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 97
 port nvo3 mode access
#
interface Eth-Trunk97.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk97.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 66
#
interface Eth-Trunk97.6 mode l2
 encapsulation dot1q vid 105
 bridge-domain 337
#
interface Eth-Trunk97.7 mode l2
 encapsulation dot1q vid 104
 bridge-domain 1040
#
interface Eth-Trunk97.9 mode l2
 encapsulation dot1q vid 107
 bridge-domain 279
#
interface Eth-Trunk97.13 mode l2
 encapsulation dot1q vid 111
 bridge-domain 208
#
interface Eth-Trunk97.15 mode l2
 encapsulation dot1q vid 113
 bridge-domain 210
#
interface Eth-Trunk97.18 mode l2
 encapsulation dot1q vid 116
 bridge-domain 87
#
interface Eth-Trunk98
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 98
 port nvo3 mode access
#
interface Eth-Trunk98.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk98.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 703
#
interface Eth-Trunk98.3 mode l2
 encapsulation dot1q vid 101
 bridge-domain 413
#
interface Eth-Trunk98.4 mode l2
 encapsulation dot1q vid 102
 bridge-domain 174
#
interface Eth-Trunk98.5 mode l2
 encapsulation dot1q vid 103
 bridge-domain 1203
#
interface Eth-Trunk98.6 mode l2
 encapsulation dot1q vid 104
 bridge-domain 281
#
interface Eth-Trunk99
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 99
 port nvo3 mode access
#
interface Eth-Trunk99.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk99.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 1044
#
interface Eth-Trunk99.3 mode l2
 encapsulation dot1q vid 101
 bridge-domain 1157
#
interface Eth-Trunk99.4 mode l2
 encapsulation dot1q vid 102
 bridge-domain 1143
#
interface Eth-Trunk99.5 mode l2
 encapsulation dot1q vid 103
 bridge-domain 1077
#
interface Eth-Trunk99.9 mode l2
 encapsulation dot1q vid 107
 bridge-domain 280
#
interface Eth-Trunk99.10 mode l2
 encapsulation dot1q vid 108
 bridge-domain 959
#
interface Eth-Trunk99.12 mode l2
 encapsulation dot1q vid 110
 bridge-domain 1078
#
interface Eth-Trunk99.13 mode l2
 encapsulation dot1q vid 111
 bridge-domain 413
#
interface Eth-Trunk99.14 mode l2
 encapsulation dot1q vid 112
 bridge-domain 279
#
interface Eth-Trunk100
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 100
 port nvo3 mode access
#
interface Eth-Trunk100.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk100.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 208
#
interface Eth-Trunk100.3 mode l2
 encapsulation dot1q vid 101
 bridge-domain 210
#
interface Eth-Trunk100.4 mode l2
 encapsulation dot1q vid 102
 bridge-domain 58
#
interface Eth-Trunk100.5 mode l2
 encapsulation dot1q vid 103
 bridge-domain 71
#
interface Eth-Trunk100.6 mode l2
 encapsulation dot1q vid 104
 bridge-domain 413
#
interface Eth-Trunk100.7 mode l2
 encapsulation dot1q vid 105
 bridge-domain 103
#
interface Eth-Trunk100.8 mode l2
 encapsulation dot1q vid 106
 bridge-domain 279
#
interface Eth-Trunk100.9 mode l2
 encapsulation dot1q vid 107
 bridge-domain 88
#
interface Eth-Trunk101
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 101
 port nvo3 mode access
#
interface Eth-Trunk101.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk101.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 1052
#
interface Eth-Trunk101.3 mode l2
 encapsulation dot1q vid 101
 bridge-domain 935
#
interface Eth-Trunk101.4 mode l2
 encapsulation dot1q vid 102
 bridge-domain 1053
#
interface Eth-Trunk101.5 mode l2
 encapsulation dot1q vid 106
 bridge-domain 934
#
interface Eth-Trunk101.7 mode l2
 encapsulation dot1q vid 107
 bridge-domain 936
#
interface Eth-Trunk101.15 mode l2
 encapsulation dot1q vid 113
 bridge-domain 71
#
interface Eth-Trunk101.16 mode l2
 encapsulation dot1q vid 114
 bridge-domain 103
#
interface Eth-Trunk102
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 102
 port nvo3 mode access
#
interface Eth-Trunk102.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk102.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 1157
#
interface Eth-Trunk102.3 mode l2
 encapsulation dot1q vid 101
 bridge-domain 1143
#
interface Eth-Trunk102.4 mode l2
 encapsulation dot1q vid 102
 bridge-domain 1125
#
interface Eth-Trunk102.5 mode l2
 encapsulation dot1q vid 103
 bridge-domain 952
#
interface Eth-Trunk102.6 mode l2
 encapsulation dot1q vid 104
 bridge-domain 71
#
interface Eth-Trunk103
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 103
 port nvo3 mode access
#
interface Eth-Trunk103.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk103.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 935
#
interface Eth-Trunk103.3 mode l2
 encapsulation dot1q vid 101
 bridge-domain 424
#
interface Eth-Trunk103.4 mode l2
 encapsulation dot1q vid 102
 bridge-domain 937
#
interface Eth-Trunk103.5 mode l2
 encapsulation dot1q vid 103
 bridge-domain 934
#
interface Eth-Trunk103.6 mode l2
 encapsulation dot1q vid 104
 bridge-domain 936
#
interface Eth-Trunk103.7 mode l2
 encapsulation dot1q vid 105
 bridge-domain 74
#
interface Eth-Trunk103.9 mode l2
 encapsulation dot1q vid 107
 bridge-domain 47
#
interface Eth-Trunk103.11 mode l2
 encapsulation dot1q vid 109
 bridge-domain 72
#
interface Eth-Trunk103.12 mode l2
 encapsulation dot1q vid 110
 bridge-domain 64
#
interface Eth-Trunk103.13 mode l2
 encapsulation dot1q vid 111
 bridge-domain 280
#
interface Eth-Trunk103.14 mode l2
 encapsulation dot1q vid 112
 bridge-domain 103
#
interface Eth-Trunk104
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 104
 port nvo3 mode access
#
interface Eth-Trunk104.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk104.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 75
#
interface Eth-Trunk104.3 mode l2
 encapsulation dot1q vid 101
 bridge-domain 319
#
interface Eth-Trunk104.4 mode l2
 encapsulation dot1q vid 102
 bridge-domain 179
#
interface Eth-Trunk104.5 mode l2
 encapsulation dot1q vid 103
 bridge-domain 71
#
interface Eth-Trunk104.6 mode l2
 encapsulation dot1q vid 104
 bridge-domain 363
#
interface Eth-Trunk104.7 mode l2
 encapsulation dot1q vid 105
 bridge-domain 105
#
interface Eth-Trunk104.16 mode l2
 encapsulation dot1q vid 116
 bridge-domain 74
#
interface Eth-Trunk105
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 105
 port nvo3 mode access
#
interface Eth-Trunk105.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk105.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 934
#
interface Eth-Trunk105.3 mode l2
 encapsulation dot1q vid 101
 bridge-domain 935
#
interface Eth-Trunk105.4 mode l2
 encapsulation dot1q vid 102
 bridge-domain 936
#
interface Eth-Trunk105.8 mode l2
 encapsulation dot1q vid 106
 bridge-domain 66
#
interface Eth-Trunk105.10 mode l2
 encapsulation dot1q vid 108
 bridge-domain 74
#
interface Eth-Trunk105.11 mode l2
 encapsulation dot1q vid 109
 bridge-domain 104
#
interface Eth-Trunk105.12 mode l2
 encapsulation dot1q vid 110
 bridge-domain 363
#
interface Eth-Trunk106
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 106
 port nvo3 mode access
#
interface Eth-Trunk106.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk106.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 1050
#
interface Eth-Trunk106.3 mode l2
 encapsulation dot1q vid 101
 bridge-domain 287
#
interface Eth-Trunk106.4 mode l2
 encapsulation dot1q vid 102
 bridge-domain 75
#
interface Eth-Trunk106.5 mode l2
 encapsulation dot1q vid 103
 bridge-domain 169
#
interface Eth-Trunk106.6 mode l2
 encapsulation dot1q vid 104
 bridge-domain 210
#
interface Eth-Trunk106.7 mode l2
 encapsulation dot1q vid 105
 bridge-domain 208
#
interface Eth-Trunk106.8 mode l2
 encapsulation dot1q vid 106
 bridge-domain 413
#
interface Eth-Trunk107
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 107
 port nvo3 mode access
#
interface Eth-Trunk107.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk107.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 1044
#
interface Eth-Trunk107.3 mode l2
 encapsulation dot1q vid 101
 bridge-domain 287
#
interface Eth-Trunk107.4 mode l2
 encapsulation dot1q vid 102
 bridge-domain 208
#
interface Eth-Trunk107.5 mode l2
 encapsulation dot1q vid 103
 bridge-domain 210
#
interface Eth-Trunk107.9 mode l2
 encapsulation dot1q vid 107
 bridge-domain 319
#
interface Eth-Trunk107.10 mode l2
 encapsulation dot1q vid 108
 bridge-domain 280
#
interface Eth-Trunk107.11 mode l2
 encapsulation dot1q vid 109
 bridge-domain 952
#
interface Eth-Trunk107.12 mode l2
 encapsulation dot1q vid 110
 bridge-domain 64
#
interface Eth-Trunk108
 stp edged-port enable
 mode lacp-dynamic
 dfs-group 1 m-lag 108
 port nvo3 mode access
#
interface Eth-Trunk108.1 mode l2
 description XNH_DETECT
 encapsulation dot1q vid 901
 bridge-domain 31
#
interface Eth-Trunk108.2 mode l2
 encapsulation dot1q vid 100
 bridge-domain 1044
#
interface Eth-Trunk108.8 mode l2
 encapsulation dot1q vid 106
 bridge-domain 403
#
interface Eth-Trunk108.9 mode l2
 encapsulation dot1q vid 107
 bridge-domain 176
#
interface Eth-Trunk108.14 mode l2
 encapsulation dot1q vid 112
 bridge-domain 71
#
interface 10GE1/0/1
 description "To-[ZY1B3F1-05P12L04U-XNH-LC3-SVR01]-TBD2"
 eth-trunk 61
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/2
 description "To-[ZY1B3F1-05P12L07U-XNH-LC3-SVR02]-TBD2"
 eth-trunk 62
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/3
 description "To-[ZY1B3F1-05P12L10U-XNH-LC3-SVR03]-TBD2"
 eth-trunk 63
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/4
 description "To-[ZY1B3F1-05P12L13U-XNH-LC3-SVR04]-TBD2"
 eth-trunk 64
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/5
 description "To-[ZY1B3F1-05P12L16U-XNH-LC3-SVR05]-TBD2"
 eth-trunk 65
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/6
 description "To-[ZY1B3F1-05P12L19U-XNH-LC3-SVR06]-TBD2"
 eth-trunk 66
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/7
 description "To-[ZY1B3F1-05P12L22U-XNH-LC3-SVR07]-TBD2"
 eth-trunk 67
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/8
 description "To-[ZY1B3F1-05P12L25U-XNH-LC3-SVR08]-TBD2"
 eth-trunk 68
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/9
 description "To-[ZY1B3F1-05P12L28U-XNH-LC3-SVR09]-TBD2"
 eth-trunk 69
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/10
 description "To-[ZY1B3F1-05P12L31U-XNH-LC3-SVR10]-TBD2"
 eth-trunk 70
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/11
 description "To-[ZY1B3F1-05P12L34U-XNH-LC3-SVR11]-TBD2"
 eth-trunk 71
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/12
 description "To-[ZY1B3F1-05P12L37U-XNH-LC3-SVR12]-TBD2"
 eth-trunk 72
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/13
 description "To-[ZY1B3F1-05P11L04U-XNH-LC3-SVR01]-TBD2"
 eth-trunk 73
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/14
 description "To-[ZY1B3F1-05P11L07U-XNH-LC3-SVR02]-TBD2"
 eth-trunk 74
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/15
 description "To-[ZY1B3F1-05P11L10U-XNH-LC3-SVR03]-TBD2"
 eth-trunk 75
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/16
 description "To-[ZY1B3F1-05P11L13U-XNH-LC3-SVR04]-TBD2"
 eth-trunk 76
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/17
 description "To-[ZY1B3F1-05P11L16U-XNH-LC3-SVR05]-TBD2"
 eth-trunk 77
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/18
 description "To-[ZY1B3F1-05P11L19U-XNH-LC3-SVR06]-TBD2"
 eth-trunk 78
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/19
 description "To-[ZY1B3F1-05P11L22U-XNH-LC3-SVR07]-TBD2"
 eth-trunk 79
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/20
 description "To-[ZY1B3F1-05P11L25U-XNH-LC3-SVR08]-TBD2"
 eth-trunk 80
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/21
 description "To-[ZY1B3F1-05P11L28U-XNH-LC3-SVR09]-TBD2"
 eth-trunk 81
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/22
 description "To-[ZY1B3F1-05P11L31U-XNH-LC3-SVR10]-TBD2"
 eth-trunk 82
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/23
 description "To-[ZY1B3F1-05P11L34U-XNH-LC3-SVR11]-TBD2"
 eth-trunk 83
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/24
 description "To-[ZY1B3F1-05P11L37U-XNH-LC3-SVR12]-TBD2"
 eth-trunk 84
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/25
 description "To-[ZY1B3F1-05P10L04U-XNH-LC3-SVR01]-TBD2"
 eth-trunk 85
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/26
 description "To-[ZY1B3F1-05P10L07U-XNH-LC3-SVR02]-TBD2"
 eth-trunk 86
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/27
 description "To-[ZY1B3F1-05P10L10U-XNH-LC3-SVR03]-TBD2"
 eth-trunk 87
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/28
 description "To-[ZY1B3F1-05P10L13U-XNH-LC3-SVR04]-TBD2"
 eth-trunk 88
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/29
 description "To-[ZY1B3F1-05P10L16U-XNH-LC3-SVR05]-TBD2"
 eth-trunk 89
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/30
 description "To-[ZY1B3F1-05P10L19U-XNH-LC3-SVR06]-TBD2"
 eth-trunk 90
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/31
 description "To-[ZY1B3F1-05P10L22U-XNH-LC3-SVR07]-TBD2"
 eth-trunk 91
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/32
 description "To-[ZY1B3F1-05P10L25U-XNH-LC3-SVR08]-TBD2"
 eth-trunk 92
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/33
 description "To-[ZY1B3F1-05P10L28U-XNH-LC3-SVR09]-TBD2"
 eth-trunk 93
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/34
 description "To-[ZY1B3F1-05P10L31U-XNH-LC3-SVR10]-TBD2"
 eth-trunk 94
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/35
 description "To-[ZY1B3F1-05P10L34U-XNH-LC3-SVR11]-TBD2"
 eth-trunk 95
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/36
 description "To-[ZY1B3F1-05P10L37U-XNH-LC3-SVR12]-TBD2"
 eth-trunk 96
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/37
 description "To-[ZY1B3F1-05P09L04U-XNH-LC3-SVR01]-TBD2"
 eth-trunk 97
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/38
 description "To-[ZY1B3F1-05P09L07U-XNH-LC3-SVR02]-TBD2"
 eth-trunk 98
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/39
 description "To-[ZY1B3F1-05P09L10U-XNH-LC3-SVR03]-TBD2"
 eth-trunk 99
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/40
 description "To-[ZY1B3F1-05P09L13U-XNH-LC3-SVR04]-TBD2"
 eth-trunk 100
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/41
 description "To-[ZY1B3F1-05P09L16U-XNH-LC3-SVR05]-TBD2"
 eth-trunk 101
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/42
 description "To-[ZY1B3F1-05P09L19U-XNH-LC3-SVR06]-TBD2"
 eth-trunk 102
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/43
 description "To-[ZY1B3F1-05P09L22U-XNH-LC3-SVR07]-TBD2"
 eth-trunk 103
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/44
 description "To-[ZY1B3F1-05P09L25U-XNH-LC3-SVR08]-TBD2"
 eth-trunk 104
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/45
 description "To-[ZY1B3F1-05P09L28U-XNH-LC3-SVR09]-TBD2"
 eth-trunk 105
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/46
 description "To-[ZY1B3F1-05P09L31U-XNH-LC3-SVR10]-TBD2"
 eth-trunk 106
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/47
 description "To-[ZY1B3F1-05P09L34U-XNH-LC3-SVR11]-TBD2"
 eth-trunk 107
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 10GE1/0/48
 description "To-[ZY1B3F1-05P09L37U-XNH-LC3-SVR12]-TBD2"
 eth-trunk 108
 storm suppression unknown-unicast 5
 storm suppression multicast packets 1000
 storm suppression broadcast packets 1000
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 10GBASE-FIBER
#
interface 40GE1/0/1
 description To-[ZY1B3F1-06P15L03U-Pub-HW12816-YWSP01]-40GE1/0/32
 eth-trunk 11
 device transceiver 40GBASE-FIBER
#
interface 40GE1/0/2
 description To-[ZY1B3F1-10P15L03U-Pub-HW12816-YWSP02]-40GE1/0/32
 eth-trunk 12
 device transceiver 40GBASE-FIBER
#
interface 40GE1/0/3
 description To-[ZY1B3F2-02P15L03U-Pub-HW12816-YWSP03]-40GE1/0/32
 eth-trunk 13
 device transceiver 40GBASE-FIBER
#
interface 40GE1/0/4
 description To-[ZY1B3F2-06P15L03U-Pub-HW12816-YWSP04]-40GE1/0/32
 eth-trunk 14
 device transceiver 40GBASE-FIBER
#
interface 40GE1/0/5
 description "To-[ZY1B3F1-05P12L44U-Pub-HW6855-YWLF01]-40GE1/0/5"
 eth-trunk 0
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 40GBASE-FIBER
#
interface 40GE1/0/6
 description "To-[ZY1B3F1-05P12L44U-Pub-HW6855-YWLF01]-40GE1/0/6"
 eth-trunk 0
 port crc-statistics trigger error-down
 trap-threshold crc-statistics 100 interval 10
 device transceiver 40GBASE-FIBER
#
interface LoopBack0
 ip address *********** ***************
#
interface LoopBack1
 ipv6 enable
 ip address *********** ***************
#
interface Nve1
 source ***********
 vni 5037 head-end peer-list protocol bgp
 vni 6007 head-end peer-list protocol bgp
 vni 6010 head-end peer-list protocol bgp
 vni 6018 head-end peer-list protocol bgp
 vni 6021 head-end peer-list protocol bgp
 vni 6053 head-end peer-list protocol bgp
 vni 6054 head-end peer-list protocol bgp
 vni 6069 head-end peer-list protocol bgp
 vni 6082 head-end peer-list protocol bgp
 vni 6085 head-end peer-list protocol bgp
 vni 6089 head-end peer-list protocol bgp
 vni 6090 head-end peer-list protocol bgp
 vni 6101 head-end peer-list protocol bgp
 vni 6105 head-end peer-list protocol bgp
 vni 6107 head-end peer-list protocol bgp
 vni 6108 head-end peer-list protocol bgp
 vni 6120 head-end peer-list protocol bgp
 vni 6134 head-end peer-list protocol bgp
 vni 6142 head-end peer-list protocol bgp
 vni 6151 head-end peer-list protocol bgp
 vni 6169 head-end peer-list protocol bgp
 vni 6189 head-end peer-list protocol bgp
 vni 6200 head-end peer-list protocol bgp
 vni 6205 head-end peer-list protocol bgp
 vni 6206 head-end peer-list protocol bgp
 vni 6226 head-end peer-list protocol bgp
 vni 6239 head-end peer-list protocol bgp
 vni 6247 head-end peer-list protocol bgp
 vni 6251 head-end peer-list protocol bgp
 vni 6403 head-end peer-list protocol bgp
 vni 6412 head-end peer-list protocol bgp
 vni 6422 head-end peer-list protocol bgp
 vni 6423 head-end peer-list protocol bgp
 vni 6424 head-end peer-list protocol bgp
 vni 6451 head-end peer-list protocol bgp
 vni 6479 head-end peer-list protocol bgp
 vni 6497 head-end peer-list protocol bgp
 vni 6499 head-end peer-list protocol bgp
 vni 6522 head-end peer-list protocol bgp
 vni 6553 head-end peer-list protocol bgp
 vni 6582 head-end peer-list protocol bgp
 vni 6611 head-end peer-list protocol bgp
 vni 6617 head-end peer-list protocol bgp
 vni 6618 head-end peer-list protocol bgp
 vni 6627 head-end peer-list protocol bgp
 vni 6632 head-end peer-list protocol bgp
 vni 6638 head-end peer-list protocol bgp
 vni 6645 head-end peer-list protocol bgp
 vni 6667 head-end peer-list protocol bgp
 vni 6687 head-end peer-list protocol bgp
 vni 6694 head-end peer-list protocol bgp
 vni 6697 head-end peer-list protocol bgp
 vni 6698 head-end peer-list protocol bgp
 vni 6717 head-end peer-list protocol bgp
 vni 6724 head-end peer-list protocol bgp
 vni 6725 head-end peer-list protocol bgp
 vni 6745 head-end peer-list protocol bgp
 vni 6746 head-end peer-list protocol bgp
 vni 6754 head-end peer-list protocol bgp
 vni 6767 head-end peer-list protocol bgp
 vni 6788 head-end peer-list protocol bgp
 vni 6818 head-end peer-list protocol bgp
 vni 6820 head-end peer-list protocol bgp
 vni 6847 head-end peer-list protocol bgp
 vni 6884 head-end peer-list protocol bgp
 vni 9560 head-end peer-list protocol bgp
 vni 9562 head-end peer-list protocol bgp
 vni 9572 head-end peer-list protocol bgp
 vni 9580 head-end peer-list protocol bgp
 vni 9595 head-end peer-list protocol bgp
 vni 9596 head-end peer-list protocol bgp
 vni 9608 head-end peer-list protocol bgp
 vni 9613 head-end peer-list protocol bgp
 vni 9636 head-end peer-list protocol bgp
 vni 9703 head-end peer-list protocol bgp
 vni 9707 head-end peer-list protocol bgp
 vni 9733 head-end peer-list protocol bgp
 vni 9758 head-end peer-list protocol bgp
 vni 9762 head-end peer-list protocol bgp
 vni 9777 head-end peer-list protocol bgp
 vni 9811 head-end peer-list protocol bgp
 vni 9826 head-end peer-list protocol bgp
 vni 9829 head-end peer-list protocol bgp
 vni 9843 head-end peer-list protocol bgp
 vni 9871 head-end peer-list protocol bgp
 vni 9876 head-end peer-list protocol bgp
 vni 9878 head-end peer-list protocol bgp
 vni 9880 head-end peer-list protocol bgp
 vni 9891 head-end peer-list protocol bgp
 vni 9906 head-end peer-list protocol bgp
 vni 9907 head-end peer-list protocol bgp
 mac-address 0000-5e00-0135
#
interface NULL0
#
monitor-link group 1
 port 40GE1/0/1 uplink
 port 40GE1/0/2 uplink
 port 40GE1/0/3 uplink
 port 40GE1/0/4 uplink
 port 10GE1/0/1 downlink 1
 port 10GE1/0/2 downlink 2
 port 10GE1/0/3 downlink 3
 port 10GE1/0/4 downlink 4
 port 10GE1/0/5 downlink 5
 port 10GE1/0/6 downlink 6
 port 10GE1/0/7 downlink 7
 port 10GE1/0/8 downlink 8
 port 10GE1/0/9 downlink 9
 port 10GE1/0/10 downlink 10
 port 10GE1/0/11 downlink 11
 port 10GE1/0/12 downlink 12
 port 10GE1/0/13 downlink 13
 port 10GE1/0/14 downlink 14
 port 10GE1/0/15 downlink 15
 port 10GE1/0/16 downlink 16
 port 10GE1/0/17 downlink 17
 port 10GE1/0/18 downlink 18
 port 10GE1/0/19 downlink 19
 port 10GE1/0/20 downlink 20
 port 10GE1/0/21 downlink 21
 port 10GE1/0/22 downlink 22
 port 10GE1/0/23 downlink 23
 port 10GE1/0/24 downlink 24
 port 10GE1/0/25 downlink 25
 port 10GE1/0/26 downlink 26
 port 10GE1/0/27 downlink 27
 port 10GE1/0/28 downlink 28
 port 10GE1/0/29 downlink 29
 port 10GE1/0/30 downlink 30
 port 10GE1/0/31 downlink 31
 port 10GE1/0/32 downlink 32
 port 10GE1/0/33 downlink 33
 port 10GE1/0/34 downlink 34
 port 10GE1/0/35 downlink 35
 port 10GE1/0/36 downlink 36
 port 10GE1/0/37 downlink 37
 port 10GE1/0/38 downlink 38
 port 10GE1/0/39 downlink 39
 port 10GE1/0/40 downlink 40
 port 10GE1/0/41 downlink 41
 port 10GE1/0/42 downlink 42
 port 10GE1/0/43 downlink 43
 port 10GE1/0/44 downlink 44
 port 10GE1/0/45 downlink 45
 port 10GE1/0/46 downlink 46
 port 10GE1/0/47 downlink 47
 port 10GE1/0/48 downlink 48
 timer recover-time 60
#
bgp 100
 router-id ***********
 peer ********** as-number 100
 peer ********** connect-interface LoopBack0
 peer ********** as-number 100
 peer ********** connect-interface LoopBack0
 peer ********** as-number 100
 peer ********** connect-interface LoopBack0
 peer ********** as-number 100
 peer ********** connect-interface LoopBack0
 #
 ipv4-family unicast
  peer ********** enable
  peer ********** enable
  peer ********** enable
  peer ********** enable
 #
 ipv4-family vpn-instance ManageOne_0000011
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance VPC-wlzys-APP_0000013
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance VPC_NMS_JT_0000089
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance VPC_ZY_WLW_APP_0000083
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance VPC_cywlwcps_APP_0000068
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance VPC_cywlwcps_DB_0000069
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance VPC_cywlwcps_DMZ_0000070
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance VPC_ex_IMS_xuanling_0000108
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance VPC_fssyw_DB_0000024
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance VPC_fssyw_DMZ_0000025
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance VPC_hxwcps_APP_0000034
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance VPC_hxwcps_DB_0000064
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance VPC_jsAIzx_APP_0000086
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance VPC_jsAIzx_DB1_50513
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance VPC_jsAIzx_DB_0000080
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance VPC_lt_APP_0000023
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance VPC_lt_DB_0000022
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance VPC_neimenggu_0000084
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance VPC_ossnls_APP_0000038
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance VPC_osswlws_APP_0000033
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance VPC_otthzs_APP_0000019
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance VPC_otthzs_DB_0000015
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance VPC_otthzs_DMZ_0000020
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance VPC_shAIzx_APP_0000071
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance VPC_shAIzx_DB_0000078
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance VPC_shanxi1_0000079
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance VPC_sichuan_APP_50298
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance VPC_wlaqs_APP_0000058
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance VPC_wlaqs_DB_0000048
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance VPC_wlaqs_DMZ_0000053
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance VPC_wlcpyfs_APP_50261
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance VPC_wlcpyfs_DB_50262
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance VPC_wlcpyfs_DMZ_50263
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance VPC_wlcpyfs_IMS_50393
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance VPC_wlcpyfs_ZNZX_50269
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance VPC_wlfzb_APP_0000066
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance VPC_wlsjs_APP_0000032
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance VPC_wlzys_DB_0000014
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance VPC_wlzys_DMZ_0000012
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance VPC_yuntian_APP_0000017
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance VPC_yuntian_DB_0000016
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance VPC_yuntian_DMZ_0000018
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance vpc-552a_0000005
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance vpc-a3ee_0000006
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance vpc-dzyuwDMZ_0000009
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance vpc-gansu_0000044
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance vpc-hebei_0000060
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance vpc-osswlws-DB2_0000073
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance vpc-qinghai_0000051
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance vpc-shanxi_0000050
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance vpc-vdc_0000054
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance vpc-yzys-APP_0000059
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv4-family vpn-instance vpc-yzys-DMZ_0000057
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv6-family vpn-instance VPC-wlzys-APP_0000013
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv6-family vpn-instance VPC_NMS_JT_0000089
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv6-family vpn-instance VPC_cywlwcps_APP_0000068
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv6-family vpn-instance VPC_cywlwcps_DB_0000069
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv6-family vpn-instance VPC_cywlwcps_DMZ_0000070
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv6-family vpn-instance VPC_ex_IMS_xuanling_0000108
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv6-family vpn-instance VPC_fssyw_DB_0000024
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv6-family vpn-instance VPC_fssyw_DMZ_0000025
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv6-family vpn-instance VPC_hxwcps_APP_0000034
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv6-family vpn-instance VPC_hxwcps_DB_0000064
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv6-family vpn-instance VPC_jsAIzx_DB1_50513
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv6-family vpn-instance VPC_lt_APP_0000023
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv6-family vpn-instance VPC_osswlws_APP_0000033
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv6-family vpn-instance VPC_otthzs_APP_0000019
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv6-family vpn-instance VPC_otthzs_DB_0000015
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv6-family vpn-instance VPC_otthzs_DMZ_0000020
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv6-family vpn-instance VPC_shAIzx_APP_0000071
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv6-family vpn-instance VPC_sichuan_APP_50298
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv6-family vpn-instance VPC_wlaqs_DMZ_0000053
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv6-family vpn-instance VPC_wlcpyfs_APP_50261
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv6-family vpn-instance VPC_wlcpyfs_DB_50262
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv6-family vpn-instance VPC_wlcpyfs_DMZ_50263
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv6-family vpn-instance VPC_wlcpyfs_IMS_50393
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv6-family vpn-instance VPC_wlcpyfs_ZNZX_50269
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv6-family vpn-instance VPC_wlsjs_APP_0000032
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv6-family vpn-instance VPC_wlzys_DMZ_0000012
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv6-family vpn-instance VPC_yuntian_DMZ_0000018
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv6-family vpn-instance vpc-vdc_0000054
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv6-family vpn-instance vpc-yzys-APP_0000059
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 ipv6-family vpn-instance vpc-yzys-DMZ_0000057
  default-route imported
  import-route direct
  import-route static
  maximum load-balancing 32  
  advertise l2vpn evpn
 #
 l2vpn-family evpn
  policy vpn-target
  peer ********** enable
  peer ********** advertise irb
  peer ********** advertise irbv6
  peer ********** enable
  peer ********** advertise irb
  peer ********** advertise irbv6
  peer ********** enable
  peer ********** advertise irb
  peer ********** advertise irbv6
  peer ********** enable
  peer ********** advertise irb
  peer ********** advertise irbv6
#
ospf 10 router-id ***********
 spf-schedule-interval intelligent-timer 50 50 50
 lsa-originate-interval intelligent-timer 50 50 50
 lsa-arrival-interval intelligent-timer 50 50 50
 area 0.0.0.0
  network *********** 0.0.0.0
  network *********** 0.0.0.0
  network *********** *******
  network *********** *******
  network *********** *******
  network *********** *******
#
ip route-static vpn-instance mgmt 0.0.0.0 0.0.0.0 MEth0/0/0 ************ description mgmt
#
snmp-agent
snmp-agent local-engineid 800007DB039C741A50A2C1
snmp-agent community read cipher %^%#a}-Q%WL5d6agCD8Y^+WQQ/%#"}deTO^.{1#XOO"T_0kt6i4&}*!TI!=LKQ(7iOw+B("`h+;>{ZH<t;d-%^%# acl 2222
#
snmp-agent sys-info version all
snmp-agent group v3 dc-admin privacy read-view rd write-view wt notify-view nt
snmp-agent target-host trap address udp-domain ***********3 params securityname cipher %^%#w/5&T2u"t5DI3>PgpX^E.ZS7Xv06&*V1(.WuY|YM%^%#
snmp-agent target-host trap address udp-domain *********** params securityname cipher %^%#ACw70["Y|C$>TkG=Yp;(q1\3+Xv`Y84qVWU[0~2K%^%#
snmp-agent target-host trap address udp-domain *********** params securityname cipher %^%#.oaaL>;(#I4@D-YL@Zm&di5AK<{LmBT8\(*gmaM1%^%#
snmp-agent target-host trap address udp-domain *********** params securityname cipher %^%#"Il#*HmGp'$qzO-0QS6AG8(n5{!',7Wu(Y1vFbsO%^%#
snmp-agent target-host trap address udp-domain *********** params securityname cipher %^%#nSX-PQqh%2mu~4Y{**n)Q]AUMffvjKLJ.<P=Q'gM%^%#
snmp-agent target-host trap address udp-domain *********** params securityname cipher %^%#V~NTV}CmnD&8/G2!Aj!;.|/j%Igm}A/n\X5;KK2T%^%#
snmp-agent target-host trap address udp-domain *********** params securityname cipher %^%#,:W/2(mBHVk8ES=x}+;AZg:(EAA\A+IjwzC*-\uG%^%#
snmp-agent target-host trap address udp-domain ************ params securityname cipher %^%#9+epL`wEnUSUK{TdZg#"U&-95u8Wz>.|-Y!b(T{K%^%#
snmp-agent target-host trap address udp-domain ***********1 params securityname cipher %^%#Y4uuV=3+vTTa]P$k^E6Ms!~dY5];iL<0<`BhSub7%^%#
snmp-agent target-host trap address udp-domain ***********2 params securityname cipher %^%#W#W`E'Wx/J%_9/!{pK75b/DrS,2VJT"3e39BvqGQ%^%#
#
snmp-agent mib-view included nt iso
snmp-agent mib-view included rd iso
snmp-agent mib-view included wt iso
snmp-agent mib-view included iso-view iso
snmp-agent usm-user v3 admin
snmp-agent usm-user v3 uhmroot
snmp-agent usm-user v3 uhmroot group dc-admin
snmp-agent usm-user v3 uhmroot authentication-mode sha cipher %^%#[t9u%9i*fIb6,bSnc:.)4CND9&^ct9>:#ZYuN9C9%^%#
snmp-agent usm-user v3 uhmroot privacy-mode aes128 cipher %^%#|aY(B5g)}L#AyBWW)KnBVM5F.8r%o~Q{TWW/sEEC%^%#
#
snmp-agent trap source LoopBack0
#
snmp-agent trap enable
#
lldp enable
lldp mdn enable
#
stelnet server enable


#
ssh server cipher aes256_ctr aes128_ctr
ssh server hmac sha2_256_96 sha2_256 sha1_96
ssh server key-exchange dh_group_exchange_sha256 dh_group_exchange_sha1 ecdh_sha2_nistp256 ecdh_sha2_nistp384 ecdh_sha2_nistp521 sm2_kep
#
ssh server dh-exchange min-len 1024
#
ssh client cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr aes256_cbc aes128_cbc 3des_cbc
#
command-privilege level 1 view global display current-configuration
command-privilege level 1 view shell screen-length
#
user-interface maximum-vty 21
#
user-interface con 0
 authentication-mode aaa
#
user-interface vty 0 20
 authentication-mode aaa
 user privilege level 3
 protocol inbound ssh
#

#
port-group 1
 group-member 10GE1/0/1
 group-member 10GE1/0/2
 group-member 10GE1/0/3
 group-member 10GE1/0/4
 group-member 10GE1/0/5
 group-member 10GE1/0/6
 group-member 10GE1/0/7
 group-member 10GE1/0/8
 group-member 10GE1/0/9
 group-member 10GE1/0/10
 group-member 10GE1/0/11
 group-member 10GE1/0/12
 group-member 10GE1/0/13
 group-member 10GE1/0/14
 group-member 10GE1/0/15
 group-member 10GE1/0/16
 group-member 10GE1/0/17
 group-member 10GE1/0/18
 group-member 10GE1/0/19
 group-member 10GE1/0/20
 group-member 10GE1/0/21
 group-member 10GE1/0/22
 group-member 10GE1/0/23
 group-member 10GE1/0/24
 group-member 10GE1/0/25
 group-member 10GE1/0/26
 group-member 10GE1/0/27
 group-member 10GE1/0/28
 group-member 10GE1/0/29
 group-member 10GE1/0/30
 group-member 10GE1/0/31
 group-member 10GE1/0/32
 group-member 10GE1/0/33
 group-member 10GE1/0/34
 group-member 10GE1/0/35
 group-member 10GE1/0/36
 group-member 10GE1/0/37
 group-member 10GE1/0/38
 group-member 10GE1/0/39
 group-member 10GE1/0/40
 group-member 10GE1/0/41
 group-member 10GE1/0/42
 group-member 10GE1/0/43
 group-member 10GE1/0/44
 group-member 10GE1/0/45
 group-member 10GE1/0/46
 group-member 10GE1/0/47
 group-member 10GE1/0/48
#
vm-manager
#
vxlan tunnel-status track exact-route
#
return
<ZY1B3F1-05P10L44U-Pub-HW6855-YWLF01>
<ZY1B3F1-05P10L44U-Pub-HW6855-YWLF01>quit
