import os
import sys

def split_file_by_delimiter(input_file, output_dir, delimiter='#\n//'):
    """
    读取输入文件，并以指定的分隔符将内容切分为多个文件
    
    Args:
        input_file (str): 输入文件的路径
        output_dir (str): 输出目录的路径
        delimiter (str): 分隔符，默认为'#'
    """
    try:
        print(f"开始处理文件: {input_file}")
        print(f"输出目录: {output_dir}")
        
        # 检查输入文件是否存在
        if not os.path.exists(input_file):
            print(f"错误: 输入文件 {input_file} 不存在")
            return
        
        # 确保输出目录存在
        if not os.path.exists(output_dir):
            print(f"创建输出目录: {output_dir}")
            os.makedirs(output_dir)
        
        # 读取输入文件
        print("读取输入文件...")
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"文件读取完成，大小: {len(content)} 字节")
        
        # 以分隔符切分内容
        sections = content.split(delimiter)
        valid_sections = [s for s in sections if s.strip()]
        print(f"找到 {len(valid_sections)} 个非空部分")
        
        # 写入切分后的文件
        count = 0
        for i, section in enumerate(sections):
            # 跳过空白部分
            if section.strip() == '':
                continue
            
            # 创建输出文件名
            output_file = os.path.join(output_dir, f'section_{i:03d}.txt')
            print(f"写入文件: {output_file}")
            
            # 写入文件内容
            with open(output_file, 'w', encoding='utf-8') as f:
                # 添加分隔符回去，除非是第一个空白部分
                if i > 0 or section.strip() != '':
                    f.write(delimiter + section)
            count += 1
        
        print(f"成功创建了 {count} 个文件")
        print(f"文件已成功切分，保存在 {output_dir} 目录下")
        
    except Exception as e:
        print(f"错误: {str(e)}")
        import traceback
        traceback.print_exc()

# 主函数
def main():
    try:
        # 设置输入文件和输出目录
        script_dir = os.path.dirname(os.path.abspath(__file__))
        print(f"脚本目录: {script_dir}")
        
        input_file = os.path.join(script_dir, "sw脱敏.txt")
        output_dir = os.path.join(script_dir, "split_sections")
        
        print(f"输入文件: {input_file}")
        print(f"输出目录: {output_dir}")
        
        # 调用函数进行文件切分
        split_file_by_delimiter(input_file, output_dir)
        
    except Exception as e:
        print(f"主函数错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    print("开始执行脚本...")
    print(f"Python版本: {sys.version}")
    print(f"当前工作目录: {os.getcwd()}")
    main()
    print("脚本执行完成")