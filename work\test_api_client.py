import json
import logging
import argparse
from api_client import VectorSearchClient

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('test_api_client')

def test_vector_search(knowledge_base_id: str, query: str = "LTE是什么", top_k: int = 2, similarity: float = 0.5):
    """
    测试向量搜索API
    
    Args:
        knowledge_base_id: 知识库ID
        query: 查询文本
        top_k: 返回结果数量
        similarity: 相似度阈值
    """
    try:
        # 创建向量搜索客户端
        vector_client = VectorSearchClient(
            base_url="http://10.186.2.176:10010/CUCCAI-intelligent-agent/vectorSearchApi/",
            app_id="urdDUFiZhKrZi",
            default_similarity=similarity,
            timeout=15,  # 增加超时时间
            max_retries=3
        )
        
        # 执行搜索
        logger.info(f"开始执行向量搜索，查询：{query}，知识库ID：{knowledge_base_id}")
        result = vector_client.search(
            query=query,
            category_id=knowledge_base_id,
            top_k=top_k
        )
        
        # 检查结果
        if result.get("success") is False:
            logger.error(f"搜索失败: {result.get('error')}")
            print(f"\n搜索失败: {result.get('error')}")
            return
        
        # 打印结果
        print("\n搜索结果:")
        print(json.dumps(result, ensure_ascii=False, indent=2))
        
        # 分析结果
        if "data" in result and result["data"]:
            print("\n找到的相关内容:")
            for i, item in enumerate(result["data"]):
                print(f"\n结果 {i+1}:")
                if "content" in item:
                    print(f"内容: {item['content']}")
                if "score" in item:
                    print(f"相似度分数: {item['score']}")
        else:
            print("\n未找到相关内容")
            
    except Exception as e:
        logger.exception(f"测试过程中发生异常: {e}")
        print(f"\n测试过程中发生异常: {e}")

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="测试向量搜索API")
    parser.add_argument("--kb_id", required=True, help="知识库ID")
    parser.add_argument("--query", default="LTE是什么", help="查询文本")
    parser.add_argument("--top_k", type=int, default=2, help="返回结果数量")
    parser.add_argument("--similarity", type=float, default=0.5, help="相似度阈值")
    
    args = parser.parse_args()
    
    # 执行测试
    test_vector_search(
        knowledge_base_id=args.kb_id,
        query=args.query,
        top_k=args.top_k,
        similarity=args.similarity
    )

if __name__ == "__main__":
    main()