def save_first_lines(input_file, output_file, line_count):
        try:
            # 打开输入文件和输出文件
            with open(input_file, 'r', encoding='utf-8') as infile, \
                 open(output_file, 'w', encoding='utf-8') as outfile:
                
                # 读取并写入指定行数
                for i, line in enumerate(infile, 1):
                    outfile.write(line)
                    if i >= line_count:
                        break
                
            print(f"成功将前{line_count}行保存到 {output_file}")
            
        except FileNotFoundError:
            print(f"错误：找不到文件 {input_file}")
        except Exception as e:
            print(f"发生错误：{str(e)}")

if __name__ == "__main__":
    # 配置文件路径和要保存的行数
    input_filename = "fw.txt"
    output_filename = "fw去重.txt"
        
    lines_to_save = 70204
        
    # 执行保存操作
    save_first_lines(input_filename, output_filename, lines_to_save)
    